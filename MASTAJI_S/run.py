#!/usr/bin/env python3
"""
Flask应用启动文件
"""
import os
from app import create_app, socketio

# 从环境变量获取配置，默认为development
config_name = os.environ.get('FLASK_CONFIG', 'development')

# 创建应用实例
app, socketio = create_app(config_name)

if __name__ == '__main__':
    # 从环境变量获取主机和端口配置
    host = os.environ.get('FLASK_HOST', '0.0.0.0')
    port = int(os.environ.get('FLASK_PORT', 8080))
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    print(f"启动Flask应用...")
    print(f"配置环境: {config_name}")
    print(f"访问地址: http://{host}:{port}")
    
    socketio.run(app, host=host, port=port, debug=debug)
