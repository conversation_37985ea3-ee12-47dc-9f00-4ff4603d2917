# 外键约束问题修复总结

## 问题描述
在删除课程安排时遇到外键约束错误，导致删除操作失败。这是因为 `course_schedules` 表被多个其他表引用，在删除时需要按正确的顺序处理所有关联记录。

## 涉及的表和外键关系

### course_schedules 表被以下表引用：
1. **danmaku** - 弹幕数据
2. **class_attendance** - 签到记录  
3. **homework** - 作业
4. **teacher_materials** - 教师课件
5. **class_groups** - 课堂分组
6. **class_reports** - 课堂报告

### 间接关联的表：
1. **group_members** - 分组成员（通过 class_groups 间接关联）
2. **homework_results** - 作业成绩（通过 homework 间接关联）

## 修复方案

### 1. 创建通用删除函数
创建了 `app/utils/database_helpers.py` 文件，包含以下通用函数：

- `delete_course_schedule_cascade()` - 级联删除课程安排
- `delete_homework_cascade()` - 级联删除作业
- `delete_class_group_cascade()` - 级联删除分组
- `delete_student_cascade()` - 级联删除学生
- `delete_teacher_cascade()` - 级联删除教师
- `delete_class_cascade()` - 级联删除班级

### 2. 正确的删除顺序
按以下顺序删除记录以避免外键约束冲突：

1. 删除分组成员数据（group_members）
2. 删除作业成绩记录（homework_results）
3. 删除签到记录（class_attendance）
4. 删除弹幕数据（danmaku）
5. 删除作业（homework）
6. 删除教师课件（teacher_materials）
7. 删除分组数据（class_groups）
8. 删除课堂报告（class_reports）
9. 删除其他可能的关联表（如 exams）
10. 最后删除课程安排本身（course_schedules）

### 3. 修复的文件

#### app/views/admin.py
- 修复了 `delete_course()` 函数
- 修复了 `delete_course_schedule()` 函数
- 引入了通用删除函数

#### app/views/teacher/course_management.py
- 修复了 `delete_course_schedule()` 函数
- 引入了通用删除函数

## 测试验证

创建了全面的测试用例验证修复效果：

1. ✅ 创建包含所有关联数据的测试场景
2. ✅ 验证课程安排删除成功
3. ✅ 验证所有关联记录被正确删除：
   - 签到记录
   - 分组数据
   - 分组成员
   - 作业数据
   - 作业成绩
   - 其他关联数据

## 优势

1. **统一性** - 所有删除操作使用相同的逻辑
2. **可维护性** - 集中管理删除逻辑，便于维护
3. **可扩展性** - 新增表时只需更新通用函数
4. **安全性** - 确保数据完整性，避免孤立记录
5. **性能** - 减少数据库查询次数

## 使用方法

```python
from app.utils.database_helpers import delete_course_schedule_cascade

# 在需要删除课程安排的地方
cursor = conn.cursor()
delete_course_schedule_cascade(cursor, schedule_id)
conn.commit()
```

## 注意事项

1. 删除操作是不可逆的，请确保在删除前进行必要的确认
2. 在生产环境中建议添加软删除机制
3. 建议在删除前备份重要数据
4. 如果添加新的关联表，需要更新通用删除函数

## 后续建议

1. 考虑实现软删除机制，标记删除而不是物理删除
2. 添加删除操作的审计日志
3. 实现批量删除操作的事务回滚机制
4. 添加删除前的数据导出功能
