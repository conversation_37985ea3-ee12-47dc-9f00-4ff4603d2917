import functools
from flask import session, redirect, url_for, request

def login_required(user_type=None):
    """登录验证装饰器
    
    Args:
        user_type: 用户类型，可选值：'student', 'teacher', 'admin'，None表示任意已登录用户
    """
    def decorator(view):
        @functools.wraps(view)
        def wrapped_view(**kwargs):
            if user_type == 'student':
                if 'student_id' not in session:
                    return redirect(url_for('auth.login'))
            elif user_type == 'teacher':
                if 'teacher_id' not in session:
                    return redirect(url_for('auth.login'))
            elif user_type == 'admin':
                # 管理员验证逻辑（暂时简化）
                if 'admin_id' not in session:
                    return redirect(url_for('auth.login'))
            else:
                # 任意已登录用户
                if 'student_id' not in session and 'teacher_id' not in session and 'admin_id' not in session:
                    return redirect(url_for('auth.login'))
            
            return view(**kwargs)
        return wrapped_view
    return decorator

def student_required(view):
    """学生登录验证装饰器"""
    return login_required('student')(view)

def teacher_required(view):
    """教师登录验证装饰器"""
    return login_required('teacher')(view)

def admin_required(view):
    """管理员登录验证装饰器"""
    return login_required('admin')(view)
