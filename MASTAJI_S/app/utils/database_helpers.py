"""
数据库操作辅助函数
"""

def delete_course_schedule_cascade(cursor, schedule_id):
    """
    级联删除课程安排及所有关联记录的通用函数
    
    Args:
        cursor: 数据库游标
        schedule_id: 课程安排ID
    """
    # 按正确的顺序删除关联记录，避免外键约束冲突
    
    # 1. 删除分组成员数据（通过分组ID间接关联）
    cursor.execute("""
        DELETE FROM group_members 
        WHERE group_id IN (
            SELECT id FROM class_groups WHERE course_schedule_id = ?
        )
    """, (schedule_id,))

    # 2. 删除作业成绩记录（通过作业ID间接关联）
    cursor.execute("""
        DELETE FROM homework_results 
        WHERE homework_id IN (
            SELECT id FROM homework WHERE course_schedule_id = ?
        )
    """, (schedule_id,))

    # 3. 删除关联的签到记录
    cursor.execute("DELETE FROM class_attendance WHERE course_schedule_id = ?", (schedule_id,))

    # 4. 删除弹幕数据
    cursor.execute("DELETE FROM danmaku WHERE course_schedule_id = ?", (schedule_id,))

    # 5. 删除关联的作业
    cursor.execute("DELETE FROM homework WHERE course_schedule_id = ?", (schedule_id,))

    # 6. 删除教师课件
    cursor.execute("DELETE FROM teacher_materials WHERE course_schedule_id = ?", (schedule_id,))

    # 7. 删除分组数据
    cursor.execute("DELETE FROM class_groups WHERE course_schedule_id = ?", (schedule_id,))

    # 8. 删除课堂报告
    cursor.execute("DELETE FROM class_reports WHERE course_schedule_id = ?", (schedule_id,))

    # 9. 删除其他可能的关联表（如果存在）
    try:
        cursor.execute("DELETE FROM exams WHERE course_schedule_id = ?", (schedule_id,))
    except:
        pass  # 如果表不存在，忽略错误

    # 10. 最后删除课程安排本身
    cursor.execute("DELETE FROM course_schedules WHERE id = ?", (schedule_id,))


def delete_homework_cascade(cursor, homework_id):
    """
    级联删除作业及所有关联记录的通用函数
    
    Args:
        cursor: 数据库游标
        homework_id: 作业ID
    """
    # 1. 删除作业成绩记录
    cursor.execute("DELETE FROM homework_results WHERE homework_id = ?", (homework_id,))
    
    # 2. 删除作业本身
    cursor.execute("DELETE FROM homework WHERE id = ?", (homework_id,))


def delete_class_group_cascade(cursor, group_id):
    """
    级联删除分组及所有关联记录的通用函数
    
    Args:
        cursor: 数据库游标
        group_id: 分组ID
    """
    # 1. 删除分组成员
    cursor.execute("DELETE FROM group_members WHERE group_id = ?", (group_id,))
    
    # 2. 删除分组本身
    cursor.execute("DELETE FROM class_groups WHERE id = ?", (group_id,))


def delete_student_cascade(cursor, student_id):
    """
    级联删除学生及所有关联记录的通用函数
    
    Args:
        cursor: 数据库游标
        student_id: 学生ID
    """
    # 1. 删除学生的作业成绩
    cursor.execute("DELETE FROM homework_results WHERE student_id = ?", (student_id,))
    
    # 2. 删除学生的签到记录
    cursor.execute("DELETE FROM class_attendance WHERE student_id = ?", (student_id,))
    
    # 3. 删除学生的弹幕记录
    cursor.execute("DELETE FROM danmaku WHERE student_id = ?", (student_id,))
    
    # 4. 删除学生的分组成员记录
    cursor.execute("DELETE FROM group_members WHERE student_id = ?", (student_id,))
    
    # 5. 删除学生本身
    cursor.execute("DELETE FROM students WHERE student_id = ?", (student_id,))


def delete_teacher_cascade(cursor, teacher_id):
    """
    级联删除教师及所有关联记录的通用函数
    
    Args:
        cursor: 数据库游标
        teacher_id: 教师ID
    """
    # 1. 获取教师的所有课程安排
    cursor.execute("SELECT id FROM course_schedules WHERE teacher_id = ?", (teacher_id,))
    schedules = cursor.fetchall()
    
    # 2. 删除每个课程安排及其关联记录
    for schedule in schedules:
        delete_course_schedule_cascade(cursor, schedule[0])
    
    # 3. 删除教师的课件
    cursor.execute("DELETE FROM teacher_materials WHERE teacher_id = ?", (teacher_id,))
    
    # 4. 删除教师的文件夹
    cursor.execute("DELETE FROM teacher_folders WHERE teacher_id = ?", (teacher_id,))
    
    # 5. 删除教师的习题
    cursor.execute("DELETE FROM exercises WHERE teacher_id = ?", (teacher_id,))
    
    # 6. 删除教师的试卷
    cursor.execute("DELETE FROM papers WHERE teacher_id = ?", (teacher_id,))
    
    # 7. 删除教师本身
    cursor.execute("DELETE FROM teachers WHERE teacher_id = ?", (teacher_id,))


def delete_class_cascade(cursor, class_id):
    """
    级联删除班级及所有关联记录的通用函数
    
    Args:
        cursor: 数据库游标
        class_id: 班级ID
    """
    # 1. 获取班级的所有学生
    cursor.execute("SELECT student_id FROM students WHERE class_id = ?", (class_id,))
    students = cursor.fetchall()
    
    # 2. 删除每个学生及其关联记录
    for student in students:
        delete_student_cascade(cursor, student[0])
    
    # 3. 获取班级的所有课程安排
    cursor.execute("SELECT id FROM course_schedules WHERE class_id = ?", (class_id,))
    schedules = cursor.fetchall()
    
    # 4. 删除每个课程安排及其关联记录
    for schedule in schedules:
        delete_course_schedule_cascade(cursor, schedule[0])
    
    # 5. 删除班级本身
    cursor.execute("DELETE FROM classes WHERE id = ?", (class_id,))
