"""
桌面捕获服务模块
使用FFmpeg捕获桌面并推流到MediaMTX服务器
"""
import subprocess
import threading
import time
import os
import signal
import psutil
from flask import current_app


class DesktopCaptureService:
    """桌面捕获服务类"""
    
    def __init__(self):
        self.ffmpeg_process = None
        self.mediamtx_process = None
        self.is_capturing = False
        self.capture_thread = None
        
    def start_mediamtx(self):
        """启动MediaMTX服务器"""
        try:
            # 检查MediaMTX是否已经运行
            if self.is_mediamtx_running():
                print("MediaMTX已经在运行")
                return True
                
            config_path = current_app.config['MEDIAMTX_CONFIG_PATH']
            bin_path = current_app.config['MEDIAMTX_BIN_PATH']
            
            # 检查配置文件是否存在
            if not os.path.exists(config_path):
                print(f"MediaMTX配置文件不存在: {config_path}")
                return False
                
            # 检查二进制文件是否存在
            if not os.path.exists(bin_path):
                print(f"MediaMTX二进制文件不存在: {bin_path}")
                return False
                
            # 启动MediaMTX
            cmd = [bin_path, config_path]
            self.mediamtx_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )
            
            # 等待服务启动
            time.sleep(2)
            
            if self.mediamtx_process.poll() is None:
                print("MediaMTX启动成功")
                return True
            else:
                print("MediaMTX启动失败")
                return False
                
        except Exception as e:
            print(f"启动MediaMTX时出错: {str(e)}")
            return False
    
    def stop_mediamtx(self):
        """停止MediaMTX服务器"""
        try:
            if self.mediamtx_process and self.mediamtx_process.poll() is None:
                # 发送SIGTERM信号
                os.killpg(os.getpgid(self.mediamtx_process.pid), signal.SIGTERM)
                self.mediamtx_process.wait(timeout=5)
                print("MediaMTX已停止")
            
            # 清理可能残留的进程
            self._kill_mediamtx_processes()
            
        except Exception as e:
            print(f"停止MediaMTX时出错: {str(e)}")
    
    def is_mediamtx_running(self):
        """检查MediaMTX是否正在运行"""
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                if 'mediamtx' in proc.info['name'].lower():
                    return True
            return False
        except Exception:
            return False
    
    def _kill_mediamtx_processes(self):
        """强制终止所有MediaMTX进程"""
        try:
            for proc in psutil.process_iter(['pid', 'name']):
                if 'mediamtx' in proc.info['name'].lower():
                    proc.kill()
        except Exception:
            pass

    def _kill_ffmpeg_processes(self):
        """强制终止所有FFmpeg进程"""
        print("清理残留的FFmpeg进程...")
        try:
            killed_count = 0
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    # 检查是否是FFmpeg进程且包含桌面捕获相关参数
                    if (proc.info['name'] and 'ffmpeg' in proc.info['name'].lower() and
                        proc.info['cmdline'] and any('x11grab' in str(cmd) for cmd in proc.info['cmdline'])):
                        print(f"发现FFmpeg桌面捕获进程 (PID: {proc.info['pid']})，正在终止...")
                        proc.kill()
                        killed_count += 1
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                except Exception as e:
                    print(f"终止FFmpeg进程时出错: {str(e)}")

            if killed_count > 0:
                print(f"已终止 {killed_count} 个FFmpeg进程")
            else:
                print("未发现需要清理的FFmpeg进程")

        except Exception as e:
            print(f"清理FFmpeg进程时出错: {str(e)}")
    
    def start_desktop_capture(self):
        """开始桌面捕获"""
        if self.is_capturing:
            return {"status": "error", "message": "桌面捕获已在运行"}
        
        # 确保MediaMTX正在运行
        if not self.start_mediamtx():
            return {"status": "error", "message": "无法启动MediaMTX服务"}
        
        try:
            # 构建FFmpeg命令
            rtsp_url = f"rtsp://{current_app.config['MEDIAMTX_HOST']}:{current_app.config['MEDIAMTX_RTSP_PORT']}/{current_app.config['DESKTOP_STREAM_NAME']}"
            
            # Linux桌面捕获命令
            cmd = [
                current_app.config['FFMPEG_PATH'],
                '-f', 'x11grab',
                '-video_size', '3840x2160',
                '-framerate', '30',
                '-i', ':0.0',
                '-c:v', 'libx264',
                '-preset', 'ultrafast',
                '-tune', 'zerolatency',
                '-pix_fmt', 'yuv420p',
                '-f', 'rtsp',
                rtsp_url
            ]
            
            # 启动FFmpeg进程
            self.ffmpeg_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                preexec_fn=os.setsid
            )
            
            self.is_capturing = True
            
            # 启动监控线程
            self.capture_thread = threading.Thread(target=self._monitor_capture)
            self.capture_thread.daemon = True
            self.capture_thread.start()
            
            return {"status": "success", "message": "桌面捕获已开始"}
            
        except Exception as e:
            return {"status": "error", "message": f"启动桌面捕获失败: {str(e)}"}
    
    def stop_desktop_capture(self):
        """停止桌面捕获"""
        print("开始停止桌面捕获...")

        # 首先设置停止标志
        self.is_capturing = False

        try:
            # 停止FFmpeg进程
            if self.ffmpeg_process and self.ffmpeg_process.poll() is None:
                print(f"正在停止FFmpeg进程 (PID: {self.ffmpeg_process.pid})")
                try:
                    # 尝试优雅地终止进程组
                    os.killpg(os.getpgid(self.ffmpeg_process.pid), signal.SIGTERM)
                    # 等待进程结束
                    self.ffmpeg_process.wait(timeout=5)
                    print("FFmpeg进程已优雅停止")
                except subprocess.TimeoutExpired:
                    print("FFmpeg进程未在超时时间内停止，强制终止...")
                    # 强制终止进程组
                    try:
                        os.killpg(os.getpgid(self.ffmpeg_process.pid), signal.SIGKILL)
                        self.ffmpeg_process.wait(timeout=2)
                        print("FFmpeg进程已强制终止")
                    except Exception as kill_error:
                        print(f"强制终止FFmpeg进程失败: {str(kill_error)}")
                except ProcessLookupError:
                    print("FFmpeg进程已不存在")
                except Exception as term_error:
                    print(f"终止FFmpeg进程时出错: {str(term_error)}")
            else:
                print("FFmpeg进程未运行或已停止")

            # 清理可能残留的FFmpeg进程
            self._kill_ffmpeg_processes()

            # 重置进程引用
            self.ffmpeg_process = None

            print("桌面捕获已成功停止")
            return {"status": "success", "message": "桌面捕获已停止"}

        except Exception as e:
            print(f"停止桌面捕获时发生错误: {str(e)}")
            # 即使出错也要尝试清理
            try:
                self._kill_ffmpeg_processes()
                self.ffmpeg_process = None
            except:
                pass
            return {"status": "error", "message": f"停止桌面捕获失败: {str(e)}"}
    
    def _monitor_capture(self):
        """监控捕获进程"""
        while self.is_capturing and self.ffmpeg_process:
            if self.ffmpeg_process.poll() is not None:
                # 进程已结束
                self.is_capturing = False
                break
            time.sleep(1)
    
    def get_stream_status(self):
        """获取流状态"""
        # 检查FFmpeg进程是否真的在运行
        ffmpeg_running = (self.ffmpeg_process is not None and
                         self.ffmpeg_process.poll() is None)

        # 如果进程已停止但状态还是True，更新状态
        if self.is_capturing and not ffmpeg_running:
            print("检测到FFmpeg进程已停止，更新捕获状态")
            self.is_capturing = False

        return {
            "mediamtx_running": self.is_mediamtx_running(),
            "capturing": self.is_capturing and ffmpeg_running,
            "ffmpeg_process_running": ffmpeg_running,
            "stream_url": {
                "rtsp": f"rtsp://{current_app.config['MEDIAMTX_HOST']}:{current_app.config['MEDIAMTX_RTSP_PORT']}/{current_app.config['DESKTOP_STREAM_NAME']}",
                "hls": f"http://{current_app.config['MEDIAMTX_HOST']}:{current_app.config['MEDIAMTX_HLS_PORT']}/{current_app.config['DESKTOP_STREAM_NAME']}/index.m3u8"
            }
        }
    
    def cleanup(self):
        """清理资源"""
        print("开始清理桌面捕获服务资源...")
        self.stop_desktop_capture()
        self.stop_mediamtx()
        print("桌面捕获服务资源清理完成")


# 全局实例
desktop_capture_service = DesktopCaptureService()
