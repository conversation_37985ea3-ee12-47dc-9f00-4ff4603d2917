from flask import Blueprint, render_template, request, redirect, url_for, session, flash
from app.models.database import get_db
from app.utils.helpers import verify_password
from datetime import datetime

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    """统一登录页面"""
    if request.method == 'POST':
        user_id = request.form.get('user_id')
        password = request.form.get('password')

        if not user_id:
            return render_template('login.html', error='用户ID不能为空')

        if not password:
            return render_template('login.html', error='密码不能为空')

        conn = get_db()
        cursor = conn.cursor()

        # 首先检查是否为学生
        cursor.execute("SELECT student_id, name, password FROM students WHERE student_id = ?", (user_id,))
        student = cursor.fetchone()

        if student:
            # 验证学生密码
            if verify_password(password, student['password']):
                conn.close()
                # 只清除学生相关的会话数据，保留教师的会话数据
                for key in list(session.keys()):
                    if key.startswith('student_'):
                        session.pop(key, None)
                # 设置学生会话
                session.permanent = True
                session['student_id'] = student['student_id']
                session['student_name'] = student['name']
                session['student_number'] = student['student_id']
                session['user_type'] = 'student'

                return redirect(url_for('student.index'))

        # 检查是否为教师
        cursor.execute("SELECT teacher_id, name, password FROM teachers WHERE teacher_id = ?", (user_id,))
        teacher = cursor.fetchone()

        if teacher:
            # 验证教师密码
            if verify_password(password, teacher['password']):
                conn.close()
                # 只清除教师相关的会话数据，保留学生的会话数据
                for key in list(session.keys()):
                    if key.startswith('teacher_'):
                        session.pop(key, None)
                # 设置教师会话
                session.permanent = True
                session['teacher_id'] = teacher['teacher_id']
                session['teacher_name'] = teacher['name']
                session['teacher_number'] = teacher['teacher_id']
                session['user_type'] = 'teacher'

                return redirect(url_for('dashboard.index'))

        conn.close()
        return render_template('login.html', error='用户ID或密码错误')

    return render_template('login.html')

@auth_bp.route('/logout')
def logout():
    """统一登出"""
    user_type = request.args.get('type', 'all')

    if user_type == 'student':
        # 只清除学生相关的会话数据
        for key in list(session.keys()):
            if key.startswith('student_'):
                session.pop(key, None)
    elif user_type == 'teacher':
        # 只清除教师相关的会话数据
        for key in list(session.keys()):
            if key.startswith('teacher_'):
                session.pop(key, None)
    else:
        # 清除所有会话数据
        session.clear()

    return redirect(url_for('auth.login'))
