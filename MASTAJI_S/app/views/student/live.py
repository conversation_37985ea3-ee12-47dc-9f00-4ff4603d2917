from flask import render_template, session
from app.models.database import get_db
from app.utils.decorators import student_required

def register_routes(bp):
    """注册直播相关路由"""
    
    @bp.route('/live')
    @student_required
    def live_player():
        """学生观看直播页面"""
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT student_id, name, class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
        student = cursor.fetchone()
        conn.close()
        return render_template("student/player.html", current_page="live", student=student)
