from flask import Blueprint, render_template, request, jsonify, session, redirect, url_for
from app.models.database import get_db
from app.utils.decorators import student_required
import json
import uuid
from datetime import datetime

def register_routes(bp):
    """注册学生作业相关路由"""
    @bp.route('/homework')
    @student_required
    def student_homework():
        """学生作业页面 - 显示所有课程的作业"""
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT student_id, name, class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
        student = cursor.fetchone()
        if not student:
            conn.close()
            return redirect(url_for('student.index'))

        cursor.execute("""
            SELECT h.id, h.title, h.description, h.created_at, c.name as course_name,
                CASE WHEN hr.id IS NOT NULL THEN 1 ELSE 0 END as is_submitted,
                hr.score, hr.submitted_at
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            JOIN courses c ON cs.course_id = c.id
            LEFT JOIN homework_results hr ON h.id = hr.homework_id AND hr.student_id = ?
            WHERE cs.class_id = ?
            ORDER BY h.created_at DESC
        """, (student['student_id'], student['class_id']))
        homeworks = cursor.fetchall()
        conn.close()
        return render_template("student/homework.html", student=student, homeworks=homeworks, current_page="homework")

    @bp.route('/exam/do/<exam_id>', methods=['GET'])
    @student_required
    def take_exam(exam_id):
        """进入做题页面，渲染试卷模板"""
        conn = get_db()
        cursor = conn.cursor()
        # 获取学生信息
        cursor.execute("SELECT student_id, name, class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
        student = cursor.fetchone()
        if not student:
            conn.close()
            return redirect(url_for('student.index'))

        # 检查是否已提交
        cursor.execute("SELECT id FROM homework_results WHERE homework_id = ? AND student_id = ?", (exam_id, student['student_id']))
        if cursor.fetchone():
            conn.close()
            return redirect(url_for('student.student_homework'))

        # 获取试卷数据
        cursor.execute("""
            SELECT h.title, h.description, h.data
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            WHERE h.id = ? AND cs.class_id = ?
        """, (exam_id, student['class_id']))
        exam_row = cursor.fetchone()
        conn.close()
        if not exam_row:
            return redirect(url_for('student.student_homework'))

        exam_data = json.loads(exam_row['data']) if exam_row['data'] else {}
        return render_template(
            "student/student_homework.html",
            student=student,
            current_page="homework",
            exam_id=exam_id,
            exam_title=exam_row['title'],
            exam_description=exam_row['description'],
            exam_data=exam_data,
        )

    # ---------- 以下接口用于前端异步获取试卷数据及提交答卷 ----------
    @bp.route('/exam/<exam_id>', methods=['GET'])
    @student_required
    def get_exam(exam_id):
        """获取试卷 JSON 数据（前端 AJAX 使用）"""
        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT student_id, name, class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
        student = cursor.fetchone()
        if not student:
            conn.close()
            return jsonify({"status": "error", "message": "学生信息不存在"}), 403

        # 不允许已提交再次获取
        cursor.execute("SELECT id FROM homework_results WHERE homework_id = ? AND student_id = ?", (exam_id, student['student_id']))
        if cursor.fetchone():
            conn.close()
            return jsonify({"status": "error", "message": "您已经提交过该作业，无法重复作答"}), 403

        cursor.execute("""
            SELECT h.data
            FROM homework h
            JOIN course_schedules cs ON h.course_schedule_id = cs.id
            WHERE h.id = ? AND cs.class_id = ?
        """, (exam_id, student['class_id']))
        exam_row = cursor.fetchone()
        conn.close()
        if not exam_row:
            return jsonify({"status": "error", "message": "试卷不存在或您无权限访问"}), 404

        exam = json.loads(exam_row[0])
        exam["questions"] = exam.get("questions", [])
        return jsonify(exam)

    @bp.route('/exam', methods=['POST'])
    @student_required
    def student_exam():
        """提交作业答案，计算得分"""
        data = request.get_json()
        homework_id = data.get('homework_id')
        answers = data.get('answers')
        if not homework_id or answers is None:
            return jsonify({"status": "error", "message": "参数不完整"}), 400

        conn = get_db()
        cursor = conn.cursor()
        cursor.execute("SELECT student_id, name, class_id FROM students WHERE student_id = ?", (session.get('student_id'),))
        student = cursor.fetchone()
        if not student:
            conn.close()
            return jsonify({"status": "error", "message": "学生信息不存在"}), 403

        # 检查重复提交
        cursor.execute("SELECT id FROM homework_results WHERE homework_id = ? AND student_id = ?", (homework_id, student['student_id']))
        if cursor.fetchone():
            conn.close()
            return jsonify({"status": "error", "message": "您已经提交过该作业"}), 400

        cursor.execute("SELECT data FROM homework WHERE id = ?", (homework_id,))
        exam_row = cursor.fetchone()
        if not exam_row:
            conn.close()
            return jsonify({"status": "error", "message": "作业不存在"}), 404

        exam_data = json.loads(exam_row[0])
        questions = exam_data.get("questions", [])
        total_score = correct_count = wrong_count = unanswered_count = 0

        for q in questions:
            qid = q.get("id")
            user_answer = answers.get(str(qid))
            correct_answer = q.get("answer")
            score = q.get("score", 0)
            is_correct = False
            if user_answer is None:
                unanswered_count += 1
            elif q['type'] in ['multiple_choice', 'true_false', 'single', 'multiple', 'judge']:
                # 多选或判断等
                if isinstance(correct_answer, list):
                    if isinstance(user_answer, str):
                        user_answer = user_answer.split(',')
                    if sorted(user_answer) == sorted(correct_answer):
                            is_correct = True
                else:
                    if str(user_answer) == str(correct_answer):
                        is_correct = True
            elif q['type'] == 'fill_in_blank':
                if str(user_answer).strip() == str(correct_answer).strip():
                    is_correct = True

            if is_correct:
                total_score += score
                correct_count += 1
            elif user_answer is not None:
                wrong_count += 1

        result_id = str(uuid.uuid4())
        cursor.execute("""
            INSERT INTO homework_results (id, homework_id, student_id, score, data, submitted_at, answers)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        """, (result_id, homework_id, student['student_id'], total_score, json.dumps(answers), datetime.now().isoformat(), json.dumps(answers)))
        conn.commit()
        conn.close()
        return jsonify({"status": "success", "message": "作业提交成功", "score": total_score, "correct_count": correct_count, "wrong_count": wrong_count, "unanswered_count": unanswered_count})
