from flask import redirect, url_for, session
from app.utils.decorators import student_required



def register_routes(bp):
    """注册学生登录相关路由"""
    @bp.route('/login')
    def login():
        """学生登录页面（重定向到统一登录）"""
        return redirect(url_for('auth.login'))

    @bp.route('/logout')
    def logout():
        """学生登出"""
        for key in list(session.keys()):
            if key.startswith('student_'):
                session.pop(key, None)
        return redirect(url_for('auth.login'))

    @bp.route('/')
    @student_required
    def index():
        """学生系统首页 - 显示课程列表"""
        return redirect(url_for('student.live_player'))