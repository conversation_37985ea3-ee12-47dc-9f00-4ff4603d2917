"""
课件管理相关路由
"""
from flask import render_template, request, jsonify, session, current_app
from app.models.database import get_db
from app.utils.decorators import teacher_required
from app.utils.helpers import save_uploaded_file, get_file_size, get_file_type, move_physical_file, create_physical_folder, delete_physical_folder
from datetime import datetime
import uuid
import os

def register_routes(bp):
    @bp.route('/materials')
    @teacher_required
    def materials():
        """课件资源管理页面"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取当前教师的所有课件
            cursor.execute("""
                SELECT id, title, file_path, file_type, file_size, upload_time, folder_path, description
                FROM teacher_materials
                WHERE teacher_id = ? AND course_schedule_id IS NULL
                ORDER BY upload_time DESC
            """, (session.get('teacher_id'),))

            materials = cursor.fetchall()

            # 获取教师的所有文件夹路径（包括空文件夹）
            cursor.execute("""
                SELECT folder_path FROM teacher_folders
                WHERE teacher_id = ?
            """, (session.get('teacher_id'),))
            folder_rows = cursor.fetchall()

            conn.close()

            # 按文件夹路径组织数据
            folders = {'/': []}  # 确保根目录始终存在

            # 先确保所有文件夹都在字典中
            for row in folder_rows:
                path = row['folder_path'] or '/'
                if path not in folders:
                    folders[path] = []

            # 将文件分配到对应的文件夹
            for material in materials:
                material_dict = dict(material)
                folder_path = material_dict['folder_path'] or '/'

                # 创建文件夹如果不存在（如果之前未添加）
                if folder_path not in folders:
                    folders[folder_path] = []

                # 将文件添加到对应的文件夹
                if folder_path == '/':
                    if not material_dict['folder_path']:  # 只有真正属于根目录的文件才添加
                        folders['/'].append(material_dict)
                else:
                    folders[folder_path].append(material_dict)

            return render_template("teacher/materials.html", 
                                current_page="materials",
                                folders=folders,
                                current_folder='/')

        except Exception as e:
            return render_template("teacher/materials.html", 
                                current_page="materials",
                                folders={'/': []},
                                current_folder='/',
                                error=str(e))


    @bp.route('/get_materials', methods=['GET'])
    @teacher_required
    def get_materials():
        """获取课件列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取筛选参数
            keyword = request.args.get('keyword', '').strip()
            folder_path = request.args.get('folder_path', '/').strip()

            # 构建查询
            base_query = """
                SELECT id, title, file_path, file_type, file_size, upload_time, folder_path, description
                FROM teacher_materials
                WHERE teacher_id = ? AND course_schedule_id IS NULL
            """
            params = [session.get('teacher_id')]

            # 添加文件夹筛选
            if folder_path != '/':
                base_query += " AND folder_path = ?"
                params.append(folder_path)
            else:
                base_query += " AND (folder_path IS NULL OR folder_path = '/')"

            # 添加关键词筛选
            if keyword:
                base_query += " AND (title LIKE ? OR description LIKE ?)"
                params.extend([f'%{keyword}%', f'%{keyword}%'])

            base_query += " ORDER BY upload_time DESC"

            cursor.execute(base_query, params)
            materials = cursor.fetchall()

            # 获取文件夹列表
            cursor.execute("""
                SELECT DISTINCT folder_path
                FROM teacher_materials
                WHERE teacher_id = ? AND course_schedule_id IS NULL AND folder_path IS NOT NULL AND folder_path != '/'
                ORDER BY folder_path
            """, (session.get('teacher_id'),))
            
            folders = [{'path': '/', 'name': '根目录'}]
            for row in cursor.fetchall():
                folder_path = row['folder_path']
                if folder_path:
                    folder_name = folder_path.strip('/').split('/')[-1]
                    folders.append({
                        'path': folder_path,
                        'name': folder_name
                    })

            conn.close()

            return jsonify({
                "status": "success",
                "materials": [dict(material) for material in materials],
                "folders": folders
            })

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/upload_material', methods=['POST'])
    @teacher_required
    def upload_material():
        """上传课件"""
        try:
            if 'file' not in request.files:
                return jsonify({"status": "error", "message": "没有选择文件"}), 400

            file = request.files['file']
            if file.filename == '':
                return jsonify({"status": "error", "message": "没有选择文件"}), 400

            # 获取表单数据
            title = request.form.get('title', '').strip()
            description = request.form.get('description', '').strip()
            folder_path = request.form.get('folder_path', '/').strip()

            # 如果没有提供标题，使用文件名
            if not title:
                title = file.filename

            # 保存文件
            file_info = save_uploaded_file(file, 'materials')
            if not file_info:
                return jsonify({"status": "error", "message": "文件保存失败"}), 500

            # 保存到数据库
            conn = get_db()
            cursor = conn.cursor()

            material_id = str(uuid.uuid4())
            cursor.execute("""
                INSERT INTO teacher_materials (id, teacher_id, title, file_path, file_type, file_size, upload_time, folder_path, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                material_id,
                session.get('teacher_id'),
                title,
                file_info['file_path'],
                file_info['file_type'],
                file_info['file_size'],
                datetime.now().isoformat(),
                folder_path if folder_path != '/' else None,
                description
            ))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "课件上传成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/create_folder', methods=['POST'])
    @teacher_required
    def create_folder():
        """创建文件夹"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            folder_name = data.get('folder_name', '').strip()
            parent_path = data.get('parent_path', '/').strip()

            if not folder_name:
                return jsonify({"status": "error", "message": "文件夹名称不能为空"}), 400

            # 构建新文件夹路径
            if parent_path == '/':
                new_folder_path = f'/{folder_name}'
            else:
                new_folder_path = f'{parent_path}/{folder_name}'

            # 检查文件夹是否已存在
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT COUNT(*) as count FROM teacher_materials
                WHERE teacher_id = ? AND folder_path = ?
            """, (session.get('teacher_id'), new_folder_path))

            if cursor.fetchone()['count'] > 0:
                conn.close()
                return jsonify({"status": "error", "message": "文件夹已存在"}), 400

            # 创建物理文件夹
            success, message = create_physical_folder(session.get('teacher_id'), new_folder_path)
            if not success:
                return jsonify({"status": "error", "message": message}), 400

            conn.close()
            return jsonify({"status": "success", "message": "文件夹创建成功", "folder_path": new_folder_path})

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/get_materials_count', methods=['GET'])
    @teacher_required
    def get_materials_count():
        """获取课件数量统计"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT COUNT(*) as count
                FROM teacher_materials
                WHERE teacher_id = ? AND course_schedule_id IS NULL
            """, (session.get('teacher_id'),))

            result = cursor.fetchone()
            conn.close()

            return jsonify({"status": "success", "count": result['count']})

        except Exception as e:
            return jsonify({"status": "error", "message": str(e)}), 500

    @bp.route('/delete_material/<material_id>', methods=['DELETE'])
    @teacher_required
    def delete_material(material_id):
        """删除课件"""
        try:
            teacher_id = session.get('teacher_id')
            conn = get_db()
            cursor = conn.cursor()

            # 验证课件是否属于当前教师且在资源库中
            cursor.execute("""
                SELECT file_path FROM teacher_materials
                WHERE id = ? AND teacher_id = ? AND course_schedule_id IS NULL
            """, (material_id, teacher_id))

            material = cursor.fetchone()
            if not material:
                conn.close()
                return jsonify({"status": "error", "message": "课件不存在或无权限"}), 404

            # 删除数据库记录
            cursor.execute("DELETE FROM teacher_materials WHERE id = ?", (material_id,))

            # 删除物理文件
            try:
                file_path = material['file_path']
                if file_path.startswith('/uploads/'):
                    # 构建完整的文件路径
                    relative_path = file_path[9:]  # 去掉 '/uploads/' 前缀
                    full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], relative_path)
                    if os.path.exists(full_path):
                        os.remove(full_path)
                        current_app.logger.info(f"成功删除物理文件: {full_path}")
                    else:
                        current_app.logger.warning(f"物理文件不存在: {full_path}")
            except Exception as e:
                current_app.logger.error(f"删除文件时出错: {e}")

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "课件删除成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"删除失败: {str(e)}"}), 500

    @bp.route('/move_material', methods=['POST'])
    @teacher_required
    def move_material():
        """移动课件到指定文件夹"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            material_id = data.get('material_id')
            target_folder = data.get('target_folder', '/').strip()

            if not material_id:
                return jsonify({"status": "error", "message": "缺少课件ID"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 验证课件是否属于当前教师
            cursor.execute("""
                SELECT file_path, folder_path FROM teacher_materials
                WHERE id = ? AND teacher_id = ? AND course_schedule_id IS NULL
            """, (material_id, session.get('teacher_id')))

            material = cursor.fetchone()
            if not material:
                conn.close()
                return jsonify({"status": "error", "message": "课件不存在或无权限"}), 404

            # 移动物理文件
            old_path = material['file_path']
            if old_path.startswith('/uploads/'):
                relative_path = old_path[9:]  # 去掉 '/uploads/' 前缀
                teacher_id = session.get('teacher_id')

                success, message, new_relative_path = move_physical_file(teacher_id, relative_path, target_folder)

                if not success:
                    conn.close()
                    return jsonify({"status": "error", "message": message}), 400

                # 更新数据库记录
                new_file_path = f"/uploads/{new_relative_path}"
                cursor.execute("""
                    UPDATE teacher_materials
                    SET file_path = ?, folder_path = ?
                    WHERE id = ?
                """, (new_file_path, target_folder, material_id))

                conn.commit()

            conn.close()

            return jsonify({"status": "success", "message": "课件移动成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"移动失败: {str(e)}"}), 500

    @bp.route('/upload_teacher_materials', methods=['POST'])
    @teacher_required
    def upload_teacher_materials():
        """上传课件到资源库"""
        conn = None
        try:
            # 检查是否有文件被上传
            # 兼容前端不同的字段命名方式，优先使用 files[]，如果为空则尝试 files
            files = request.files.getlist('files[]') or request.files.getlist('files')
            if not files or all(file.filename == '' for file in files):
                return jsonify({"status": "error", "message": "没有选择文件"}), 400

            # 获取表单数据
            folder_path = request.form.get('folder_path', '/').strip()
            description = request.form.get('description', '').strip()

            teacher_id = session.get('teacher_id')
            uploaded_files = []
            failed_files = []

            conn = get_db()
            cursor = conn.cursor()

            # 开始事务
            conn.execute('BEGIN TRANSACTION')

            for file in files:
                if file.filename == '':
                    continue

                try:
                    # 保存文件
                    # 使用工具函数保存文件
                    success, message, filename, relative_path = save_uploaded_file(file, teacher_id, folder_path)

                    if not success:
                        failed_files.append(f"{file.filename}: {message}")
                        continue

                    # 获取文件信息
                    full_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], relative_path)
                    file_size = get_file_size(full_file_path)
                    file_type = get_file_type(filename)

                    # 生成课件ID
                    material_id = str(uuid.uuid4())
                    current_time = datetime.now().isoformat()

                    # 插入课件记录（不关联课程）
                    cursor.execute("""
                        INSERT INTO teacher_materials (id, teacher_id, title, file_path, file_type, file_size,
                                                    upload_time, folder_path, description, course_schedule_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        material_id,
                        teacher_id,
                        file.filename,
                        f"/uploads/{relative_path}",
                        file_type,
                        file_size,
                        current_time,
                        folder_path if folder_path != '/' else None,
                        description,
                        None  # 资源库中的课件不关联课程
                    ))

                    uploaded_files.append(file.filename)

                except Exception as file_error:
                    failed_files.append(f"{file.filename}: {str(file_error)}")
                    continue

            # 提交事务
            conn.commit()
            conn.close()

            if uploaded_files:
                message = f"成功上传 {len(uploaded_files)} 个课件"
                if failed_files:
                    message += f"，{len(failed_files)} 个文件失败"

                return jsonify({
                    "status": "success",
                    "message": message,
                    "files": uploaded_files,
                    "failed_files": failed_files if failed_files else []
                })
            else:
                error_message = "所有文件上传失败"
                if failed_files:
                    error_message += f": {'; '.join(failed_files[:3])}"
                return jsonify({"status": "error", "message": error_message}), 400

        except Exception as e:
            if conn:
                conn.rollback()
                conn.close()
            return jsonify({"status": "error", "message": f"上传失败: {str(e)}"}), 500

    @bp.route('/assign_material_to_class', methods=['POST'])
    @teacher_required
    def assign_material_to_class():
        """分配课件到课堂"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            material_id = data.get('material_id')
            schedule_id = data.get('schedule_id')

            if not material_id or not schedule_id:
                return jsonify({"status": "error", "message": "缺少必要参数"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 验证课件是否属于当前教师
            cursor.execute("""
                SELECT title, file_path, file_type, file_size, description FROM teacher_materials
                WHERE id = ? AND teacher_id = ? AND course_schedule_id IS NULL
            """, (material_id, session.get('teacher_id')))

            material = cursor.fetchone()
            if not material:
                conn.close()
                return jsonify({"status": "error", "message": "课件不存在或无权限"}), 404

            # 验证课程是否属于当前教师
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "课程不存在或无权限"}), 404

            # 创建新的课件记录（关联到课程）
            new_material_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()
            cursor.execute("""
                INSERT INTO teacher_materials (id, teacher_id, title, file_path, file_type, file_size,
                                            upload_time, folder_path, description, course_schedule_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                new_material_id,
                session.get('teacher_id'),
                material['title'],
                material['file_path'],
                material['file_type'],
                material['file_size'],
                current_time,
                '/',
                material['description'],
                schedule_id
            ))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "课件分配成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"分配失败: {str(e)}"}), 500

    @bp.route('/delete_folder', methods=['DELETE'])
    @teacher_required
    def delete_folder():
        """删除文件夹"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({"status": "error", "message": "无效的请求数据"}), 400

            folder_path = data.get('folder_path', '').strip()

            if not folder_path or folder_path == '/':
                return jsonify({"status": "error", "message": "不能删除根目录"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 检查文件夹是否有文件
            cursor.execute("""
                SELECT COUNT(*) as count FROM teacher_materials
                WHERE teacher_id = ? AND folder_path = ? AND course_schedule_id IS NULL
            """, (session.get('teacher_id'), folder_path))

            result = cursor.fetchone()
            if result['count'] > 0:
                conn.close()
                return jsonify({"status": "error", "message": "文件夹不为空，无法删除"}), 400

            # 检查是否有子文件夹
            cursor.execute("""
                SELECT COUNT(*) as count FROM teacher_materials
                WHERE teacher_id = ? AND folder_path LIKE ? AND course_schedule_id IS NULL
            """, (session.get('teacher_id'), folder_path + '/%'))

            result = cursor.fetchone()
            if result['count'] > 0:
                conn.close()
                return jsonify({"status": "error", "message": "文件夹包含子文件夹，无法删除"}), 400

            # 删除物理文件夹
            success, message = delete_physical_folder(session.get('teacher_id'), folder_path)
            if not success:
                conn.close()
                return jsonify({"status": "error", "message": message}), 400

            conn.close()
            return jsonify({"status": "success", "message": "文件夹删除成功"})

        except Exception as e:
            if 'conn' in locals() and conn:
                conn.close()
            return jsonify({"status": "error", "message": f"删除失败: {str(e)}"}), 500
