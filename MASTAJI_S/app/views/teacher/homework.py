"""
作业管理相关路由
"""
from flask import render_template, request, jsonify, session
from app.models.database import get_db
from app.utils.decorators import teacher_required
from datetime import datetime
import uuid
import json


def register_routes(bp):
    """注册作业管理相关路由"""
    
    @bp.route('/homework')
    @teacher_required
    def homework():
        """课后作业页面"""
        # 获取当前选中的课堂
        current_class_id = session.get('current_class_id')
        if not current_class_id:
            return render_template("teacher/homework.html", current_page="homework", current_class=None)

        conn = get_db()
        cursor = conn.cursor()

        # 获取当前课堂信息
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                   cs.start_datetime, cs.description
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (current_class_id, session.get('teacher_id')))

        current_class = cursor.fetchone()
        if not current_class:
            conn.close()
            return render_template("teacher/homework.html", current_page="homework", current_class=None)

        # 获取课后作业列表
        cursor.execute("""
            SELECT h.id, h.title, h.description, h.created_at, h.status,
                   h.deadline, h.question_count, h.submitted_count, h.total_students
            FROM homework h
            WHERE h.course_schedule_id = ? AND h.teacher_id = ?
            ORDER BY h.created_at DESC
        """, (current_class_id, session.get('teacher_id')))

        homework_list = cursor.fetchall()
        conn.close()

        return render_template("teacher/homework.html",
                              current_page="homework",
                              current_class=current_class,
                              homework_list=homework_list)

    @bp.route('/import_paper_as_homework', methods=['POST'])
    @teacher_required
    def import_paper_as_homework():
        """从资源库导入试卷作为作业"""
        try:
            paper_id = request.form.get('paper_id')
            title = request.form.get('title')
            deadline = request.form.get('deadline')
            description = request.form.get('description', '')
            course_id = request.form.get('course_id')

            if not paper_id or not title or not course_id:
                return jsonify({"status": "error", "message": "缺少必要参数"}), 400

            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取原试卷数据
            cursor.execute("""
                SELECT data FROM papers
                WHERE id = ? AND teacher_id = ?
            """, (paper_id, session.get('teacher_id')))

            paper = cursor.fetchone()
            if not paper:
                conn.close()
                return jsonify({"status": "error", "message": "试卷不存在或无权限"}), 404

            # 创建新的作业
            homework_id = str(uuid.uuid4())
            current_time = datetime.now().isoformat()

            # 解析试卷数据获取题目数量
            paper_data = json.loads(paper['data'])
            question_count = len(paper_data.get('questions', []))

            # 获取班级学生总数
            cursor.execute("""
                SELECT COUNT(*) as total
                FROM students s
                JOIN course_schedules cs ON s.class_id = cs.class_id
                WHERE cs.id = ?
            """, (course_id,))

            total_students = cursor.fetchone()['total']

            cursor.execute("""
                INSERT INTO homework (id, title, description, teacher_id, course_schedule_id, created_at, data,
                                    status, deadline, question_count, submitted_count, total_students)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (homework_id, title, description, session.get('teacher_id'), course_id, current_time,
                  paper['data'], 'draft', deadline, question_count, 0, total_students))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "作业创建成功", "homework_id": homework_id})

        except Exception as e:
            return jsonify({"status": "error", "message": f"创建作业失败: {str(e)}"}), 500

    @bp.route('/publish_homework/<homework_id>', methods=['POST'])
    @teacher_required
    def publish_homework(homework_id):
        """发布作业"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证作业权限
            cursor.execute("""
                SELECT h.id FROM homework h
                JOIN course_schedules cs ON h.course_schedule_id = cs.id
                WHERE h.id = ? AND cs.teacher_id = ?
            """, (homework_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "作业不存在或无权限"}), 404

            # 更新作业状态
            cursor.execute("""
                UPDATE homework SET status = 'published' WHERE id = ?
            """, (homework_id,))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "作业发布成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"发布失败: {str(e)}"}), 500

    @bp.route('/delete_homework/<homework_id>', methods=['DELETE'])
    @teacher_required
    def delete_homework(homework_id):
        """删除作业"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证作业权限
            cursor.execute("""
                SELECT h.id FROM homework h
                JOIN course_schedules cs ON h.course_schedule_id = cs.id
                WHERE h.id = ? AND cs.teacher_id = ?
            """, (homework_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "作业不存在或无权限"}), 404

            # 删除相关的成绩记录
            cursor.execute("DELETE FROM homework_results WHERE homework_id = ?", (homework_id,))

            # 删除作业
            cursor.execute("DELETE FROM homework WHERE id = ?", (homework_id,))

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "作业删除成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"删除失败: {str(e)}"}), 500

    @bp.route('/get_course_homework/<course_schedule_id>')
    @teacher_required
    def get_course_homework(course_schedule_id):
        """获取指定课程的作业列表"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 验证课程权限
            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取该课程的所有作业
            cursor.execute("""
                SELECT id, title, description, created_at
                FROM homework
                WHERE course_schedule_id = ?
                ORDER BY created_at DESC
            """, (course_schedule_id,))

            homework_list = cursor.fetchall()
            conn.close()

            # 转换为字典格式
            homework_data = []
            for homework in homework_list:
                homework_data.append({
                    'id': homework['id'],
                    'title': homework['title'],
                    'description': homework['description'],
                    'created_at': homework['created_at']
                })

            return jsonify({"status": "success", "homework": homework_data})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取作业列表失败: {str(e)}"}), 500

    @bp.route('/get_resource_papers')
    @teacher_required
    def get_resource_papers():
        """获取资源库中的原始试卷列表（用于导入功能）"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取资源库中的试卷，用于导入到课程中
            cursor.execute("""
                SELECT id, title, description, created_at
                FROM papers
                WHERE teacher_id = ?
                ORDER BY created_at DESC
            """, (session.get('teacher_id'),))

            papers = cursor.fetchall()
            conn.close()

            # 转换为字典格式
            papers_list = []
            for paper in papers:
                papers_list.append({
                    'id': paper['id'],
                    'title': paper['title'],
                    'description': paper['description'],
                    'created_at': paper['created_at']
                })

            return jsonify({"status": "success", "papers": papers_list})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取资源库试卷失败: {str(e)}"}), 500

    @bp.route('/view_homework/<homework_id>')
    @teacher_required
    def view_homework(homework_id):
        """查看作业详情页面"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 查询作业基本信息，验证权限
            cursor.execute("""
                SELECT h.id, h.title, h.description, h.status, h.deadline, h.data,
                       c.name as course_name, c.code as course_code, cls.name as class_name
                FROM homework h
                LEFT JOIN course_schedules cs ON h.course_schedule_id = cs.id
                LEFT JOIN courses c ON cs.course_id = c.id
                LEFT JOIN classes cls ON cs.class_id = cls.id
                WHERE h.id = ? AND h.teacher_id = ?
            """, (homework_id, session.get('teacher_id')))

            homework_row = cursor.fetchone()
            if not homework_row:
                conn.close()
                return jsonify({
                    "status": "error",
                    "message": "作业不存在或无权限"
                }), 404

            # 解析作业数据
            homework_data = json.loads(homework_row['data']) if homework_row['data'] else {}

            # 构建作业信息
            homework = {
                'id': homework_row['id'],
                'title': homework_row['title'],
                'description': homework_row['description'],
                'status': homework_row['status'],
                'deadline': homework_row['deadline'],
                'course_name': homework_row['course_name'],
                'course_code': homework_row['course_code'],
                'class_name': homework_row['class_name'],
                'questions': homework_data.get('questions', []),
                'question_count': len(homework_data.get('questions', []))
            }

            # 如果是课程作业，获取提交统计
            if homework_row['course_name']:
                # 获取班级总人数
                cursor.execute("""
                    SELECT COUNT(*) FROM students s
                    WHERE s.class_id = (
                        SELECT cs.class_id FROM course_schedules cs
                        JOIN homework h ON h.course_schedule_id = cs.id
                        WHERE h.id = ?
                    )
                """, (homework_id,))
                result = cursor.fetchone()
                total_students = result[0] if result else 0

                # 获取已提交人
                cursor.execute("""
                    SELECT COUNT(DISTINCT student_id) FROM homework_results
                    WHERE homework_id = ?
                """, (homework_id,))
                submitted_count = cursor.fetchone()[0] or 0

                homework['total_students'] = total_students
                homework['submitted_count'] = submitted_count

            conn.close()
            return render_template("teacher/view_homework.html", homework=homework, current_page="homework")

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取作业详情失败: {str(e)}"}), 500

    @bp.route('/homework_analysis/<homework_id>')
    @teacher_required
    def homework_analysis(homework_id):
        """作业分析页面"""
        # 获取当前选中的课堂
        current_class_id = session.get('current_class_id')
        if not current_class_id:
            return render_template("teacher/homework_analysis.html", current_page="homework_analysis", current_class=None)

        conn = get_db()
        cursor = conn.cursor()

        # 获取当前课堂信息
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                   cs.start_datetime, cs.description
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (current_class_id, session.get('teacher_id')))

        current_class = cursor.fetchone()

        if not current_class:
            return render_template("teacher/homework_analysis.html", current_page="homework_analysis", current_class=None)
        
        try:
            conn = get_db()
            cursor = conn.cursor()
            # 获取作业基本信息
            cursor.execute("""
                SELECT h.id, h.title, h.description, h.status, h.deadline, h.data,
                       c.name as course_name, c.code as course_code, cls.name as class_name,
                       cs.id as course_schedule_id
                FROM homework h
                LEFT JOIN course_schedules cs ON h.course_schedule_id = cs.id
                LEFT JOIN courses c ON cs.course_id = c.id
                LEFT JOIN classes cls ON cs.class_id = cls.id
                WHERE h.id = ? AND h.teacher_id = ?
            """, (homework_id, session.get('teacher_id')))

            homework_row = cursor.fetchone()
            if not homework_row:
                conn.close()
                return render_template("teacher/homework_analysis.html", homework=None, current_course=None, results=[], question_stats=[], score_distribution=[], stats={})

            # 解析作业数据
            homework_data = json.loads(homework_row['data']) if homework_row['data'] else {}

            # 构建作业信息
            homework = {
                'id': homework_row['id'],
                'title': homework_row['title'],
                'description': homework_row['description'],
                'status': homework_row['status'],
                'deadline': homework_row['deadline'],
                'course_name': homework_row['course_name'],
                'course_code': homework_row['course_code'],
                'class_name': homework_row['class_name'],
                'questions': homework_data.get('questions', []),
                'question_count': len(homework_data.get('questions', []))
            }

            # 只用homework_row里的课程信息
            current_course = {
                'id': homework_row['course_schedule_id'],
                'course_name': homework_row['course_name'],
                'course_code': homework_row['course_code'],
                'class_name': homework_row['class_name']
            } if homework_row['course_name'] else None

            # 获取作业结果
            cursor.execute("""
                SELECT hr.student_id, s.name as student_name, hr.score, hr.submitted_at, hr.answers
                FROM homework_results hr
                JOIN students s ON hr.student_id = s.student_id
                WHERE hr.homework_id = ?
            """, (homework_id,))

            results = cursor.fetchall()
            # 转换为列表字典
            results_list = []
            for row in results:
                results_list.append(dict(row) if not isinstance(row, dict) else row)

            # 题目统计
            question_stats = []
            questions = homework_data.get('questions', [])
            if questions and results_list:
                for idx, q in enumerate(questions):
                    question_stats.append({
                        'index': idx + 1,
                        'type_name': q.get('type', ''),
                        'text': q.get('question', ''),
                        'correct_answer': q.get('answer', ''),
                        'correct_count': 0,
                        'total_count': 0,
                        'correct_rate': 0.0
                    })
                for res in results_list:
                    try:
                        answers = json.loads(res['answers']) if res['answers'] else {}
                    except Exception:
                        answers = {}
                    for idx, q in enumerate(questions):
                        qid = f"q-{idx}"
                        if qid in answers:
                            question_stats[idx]['total_count'] += 1
                            if 'score' in answers.get(qid, {}):
                                if float(answers[qid]['score']) > 0:
                                    question_stats[idx]['correct_count'] += 1
                            else:
                                # 如果没有score字段，假设答案等于标准答案即为正确
                                if str(answers[qid]).strip() == str(q.get('answer', '')).strip():
                                    question_stats[idx]['correct_count'] += 1
                for stat in question_stats:
                    if stat['total_count'] > 0:
                        stat['correct_rate'] = stat['correct_count'] / stat['total_count'] * 100
                    else:
                        stat['correct_rate'] = 0.0

            # 成绩分布统计
            score_distribution = [0] * 10
            scores = []
            for res in results_list:
                try:
                    score = float(res['score'])
                except Exception:
                    score = 0
                scores.append(score)
                idx = int(score // 10)
                if idx >= 10:
                    idx = 9
                score_distribution[idx] += 1

            # 统计指标
            stats = {
                'avg_score': round(sum(scores)/len(scores), 2) if scores else 0,
                'max_score': max(scores) if scores else 0,
                'min_score': min(scores) if scores else 0,
                'pass_rate': (len([s for s in scores if s >= 60]) / len(scores) * 100) if scores else 0,
                'excellent_rate': (len([s for s in scores if s >= 85]) / len(scores) * 100) if scores else 0,
                'good_rate': (len([s for s in scores if s >= 75]) / len(scores) * 100) if scores else 0,
                'fail_rate': (len([s for s in scores if s < 60]) / len(scores) * 100) if scores else 0,
                'count': len(scores)
            }

            conn.close()
            return render_template("teacher/homework_analysis.html",
                                  homework=homework,
                                  current_course=current_course,
                                  results=results_list,
                                  question_stats=question_stats,
                                  score_distribution=score_distribution,
                                  stats=stats)

        except Exception as e:
            return render_template("teacher/homework_analysis.html", homework=None, current_course=None, results=[], question_stats=[], score_distribution=[], stats={})

    @bp.route('/create_homework')
    @teacher_required
    def create_homework():
        """新建作业页面"""
        current_class_id = session.get('current_class_id')
        if not current_class_id:
            return jsonify({'status': 'error', 'message': '未指定课程'})

        conn = get_db()
        cursor = conn.cursor()

        # 获取课程信息
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (current_class_id, session.get('teacher_id')))

        current_class = cursor.fetchone()
        if not current_class:
            conn.close()
            return jsonify({'status': 'error', 'message': '课程不存在或无权限'})

        return render_template("teacher/add_homework.html", 
                            current_page="homework", 
                            current_class=current_class)

    @bp.route('/save_homework', methods=['POST'])
    @teacher_required
    def save_homework():
        """保存作业"""
        try:
            data = request.get_json()
            
            # 验证必要字段
            required_fields = ['title', 'course_id', 'questions', 'deadline', 'status']
            for field in required_fields:
                if field not in data:
                    return jsonify({'status': 'error', 'message': f'缺少必要字段: {field}'})

            conn = get_db()
            cursor = conn.cursor()

            # 处理截止时间，添加默认时间部分
            deadline = data['deadline']
            if deadline:
                deadline = f"{deadline} 23:59:59"

            # 创建作业记录
            homework_id = str(uuid.uuid4())
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # 将题目数据转换为JSON字符串
            homework_data = {
                'questions': data['questions'],
                'total_score': data['total_score']
            }

            cursor.execute("""
                INSERT INTO homework (
                    id, title, description, course_schedule_id, teacher_id,
                    deadline, status, created_at, data, question_count
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                homework_id,
                data['title'],
                data.get('description', ''),
                data['course_id'],
                session.get('teacher_id'),
                deadline,
                data['status'],
                current_time,
                json.dumps(homework_data),
                len(data['questions'])
            ))

            conn.commit()
            conn.close()

            return jsonify({
                'status': 'success',
                'message': '作业保存成功',
                'homework_id': homework_id
            })

        except Exception as e:
            if 'conn' in locals():
                conn.rollback()
                conn.close()
            return jsonify({'status': 'error', 'message': str(e)})
