from flask import Blueprint
from . import auth, course_management, courseware, attendance, groups, homework, papers, exercises, reports, desktop_capture, materials

teacher_bp = Blueprint('teacher', __name__, url_prefix='/teacher')

# 注册子模块的路由
auth.register_routes(teacher_bp)
course_management.register_routes(teacher_bp)
courseware.register_routes(teacher_bp)
attendance.register_routes(teacher_bp)
groups.register_routes(teacher_bp)
homework.register_routes(teacher_bp)
papers.register_routes(teacher_bp)
exercises.register_routes(teacher_bp)
reports.register_routes(teacher_bp)
desktop_capture.register_routes(teacher_bp)
materials.register_routes(teacher_bp)
