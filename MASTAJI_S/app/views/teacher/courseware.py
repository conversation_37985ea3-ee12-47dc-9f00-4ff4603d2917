"""
课件管理相关路由
"""
from flask import render_template, request, jsonify, session, current_app
from app.models.database import get_db
from app.utils.decorators import teacher_required
from app.utils.helpers import save_uploaded_file, get_file_size, get_file_type, move_physical_file, create_physical_folder, delete_physical_folder
from datetime import datetime
import uuid
import os


def register_routes(bp):
    """注册课件管理相关路由"""
    
    @bp.route('/courseware')
    @teacher_required
    def courseware():
        """课件管理页面"""
        # 获取当前选中的课堂
        current_class_id = session.get('current_class_id')
        if not current_class_id:
            return render_template("teacher/courseware.html", current_page="courseware", current_class=None)

        conn = get_db()
        cursor = conn.cursor()

        # 获取当前课堂信息
        cursor.execute("""
            SELECT cs.id, c.name as course_name, c.code as course_code,
                   cl.name as classroom_name, cls.name as class_name,
                   cs.day_of_week, cs.start_time, cs.end_time, cs.status,
                   cs.start_datetime, cs.description
            FROM course_schedules cs
            JOIN courses c ON cs.course_id = c.id
            JOIN classrooms cl ON cs.classroom_id = cl.id
            JOIN classes cls ON cs.class_id = cls.id
            WHERE cs.id = ? AND cs.teacher_id = ?
        """, (current_class_id, session.get('teacher_id')))

        current_class = cursor.fetchone()
        if not current_class:
            conn.close()
            return render_template("teacher/courseware.html", current_page="courseware", current_class=None)

        # 获取课件列表
        cursor.execute("""
            SELECT tm.id, tm.title, tm.file_path, tm.file_type, tm.upload_time, tm.description,
                   CASE
                       WHEN tm.course_schedule_id IS NOT NULL THEN 'course_material'
                       ELSE 'resource_material'
                   END as source
            FROM teacher_materials tm
            WHERE tm.teacher_id = ? AND (
                tm.course_schedule_id = ? OR
                (tm.course_schedule_id IS NULL AND tm.folder_path = '/')
            )
            ORDER BY tm.upload_time DESC
        """, (session.get('teacher_id'), current_class_id))

        course_materials = cursor.fetchall()
        conn.close()

        return render_template("teacher/courseware.html",
                              current_page="courseware",
                              current_class=current_class,
                              course_materials=course_materials)

    @bp.route('/upload_course_materials/<course_schedule_id>', methods=['POST'])
    @teacher_required
    def upload_course_materials(course_schedule_id):
        """课堂上传课件"""
        conn = None
        try:
            # 验证课程权限
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 检查是否有文件被上传
            if 'files' not in request.files:
                conn.close()
                return jsonify({"status": "error", "message": "没有选择文件"}), 400

            files = request.files.getlist('files')
            if not files or all(file.filename == '' for file in files):
                conn.close()
                return jsonify({"status": "error", "message": "没有选择文件"}), 400

            teacher_id = session.get('teacher_id')
            uploaded_files = []
            failed_files = []

            # 开始事务
            conn.execute('BEGIN TRANSACTION')

            for file in files:
                if file.filename == '':
                    continue

                try:
                    # 使用工具函数保存文件到根目录
                    success, message, filename, relative_path = save_uploaded_file(file, teacher_id, '/')

                    if not success:
                        failed_files.append(f"{file.filename}: {message}")
                        continue

                    # 获取文件信息
                    full_file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], relative_path)
                    file_size = get_file_size(full_file_path)
                    file_type = get_file_type(filename)

                    # 生成课件ID
                    material_id = str(uuid.uuid4())
                    current_time = datetime.now().isoformat()

                    # 插入课件记录，关联到当前课程
                    cursor.execute("""
                        INSERT INTO teacher_materials (id, teacher_id, title, file_path, file_type, file_size, upload_time, folder_path, description, course_schedule_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        material_id,
                        teacher_id,
                        filename,  # 直接使用文件名作为标题
                        f"/uploads/{relative_path}",
                        file_type,
                        file_size,
                        current_time,
                        '/',  # 保存到根目录
                        "",  # 描述为空
                        course_schedule_id  # 关联到当前课程
                    ))

                    uploaded_files.append(filename)

                except Exception as file_error:
                    failed_files.append(f"{file.filename}: {str(file_error)}")
                    continue

            # 提交事务
            conn.commit()
            conn.close()

            if uploaded_files:
                message = f"成功上传 {len(uploaded_files)} 个课件"
                if failed_files:
                    message += f"，{len(failed_files)} 个文件失败"

                return jsonify({
                    "status": "success",
                    "message": message,
                    "files": uploaded_files,
                    "failed_files": failed_files if failed_files else []
                })
            else:
                error_message = "所有文件上传失败"
                if failed_files:
                    error_message += f": {'; '.join(failed_files[:3])}"
                return jsonify({"status": "error", "message": error_message}), 400

        except Exception as e:
            if conn:
                conn.close()
            return jsonify({"status": "error", "message": f"上传失败: {str(e)}"}), 500

    @bp.route('/delete_course_material/<material_id>', methods=['DELETE'])
    @teacher_required
    def delete_course_material(material_id):
        """删除课件（课堂使用）"""
        try:
            teacher_id = session.get('teacher_id')
            conn = get_db()
            cursor = conn.cursor()

            # 验证课件是否属于当前教师
            cursor.execute("""
                SELECT file_path FROM teacher_materials
                WHERE id = ? AND teacher_id = ?
            """, (material_id, teacher_id))

            material = cursor.fetchone()
            if not material:
                conn.close()
                return jsonify({"status": "error", "message": "课件不存在或无权限"}), 404

            # 删除数据库记录
            cursor.execute("DELETE FROM teacher_materials WHERE id = ?", (material_id,))

            # 删除物理文件
            try:
                file_path = material['file_path']
                if file_path.startswith('/uploads/'):
                    # 构建完整的文件路径
                    relative_path = file_path[9:]  # 去掉 '/uploads/' 前缀
                    full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], relative_path)
                    if os.path.exists(full_path):
                        os.remove(full_path)
                        current_app.logger.info(f"成功删除物理文件: {full_path}")
                    else:
                        current_app.logger.warning(f"物理文件不存在: {full_path}")
            except Exception as e:
                current_app.logger.error(f"删除文件时出错: {e}")

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": "课件删除成功"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"删除失败: {str(e)}"}), 500

    @bp.route('/get_resource_materials')
    @teacher_required
    def get_resource_materials():
        """获取资源库中的课件列表（用于导入功能）"""
        try:
            conn = get_db()
            cursor = conn.cursor()

            # 获取当前教师的资源库课件（没有关联课程的课件）
            cursor.execute("""
                SELECT id, title, file_type, upload_time, description
                FROM teacher_materials
                WHERE teacher_id = ? AND course_schedule_id IS NULL
                ORDER BY upload_time DESC
            """, (session.get('teacher_id'),))

            materials = cursor.fetchall()
            conn.close()

            # 转换为字典格式
            materials_list = []
            for material in materials:
                materials_list.append({
                    'id': material['id'],
                    'title': material['title'],
                    'file_type': material['file_type'],
                    'upload_time': material['upload_time'],
                    'description': material['description']
                })

            return jsonify({"status": "success", "materials": materials_list})

        except Exception as e:
            return jsonify({"status": "error", "message": f"获取资源库课件失败: {str(e)}"}), 500

    @bp.route('/import_materials/<course_schedule_id>', methods=['POST'])
    @teacher_required
    def import_materials(course_schedule_id):
        """从资源库导入课件到课堂"""
        try:
            # 验证课程权限
            conn = get_db()
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id FROM course_schedules
                WHERE id = ? AND teacher_id = ?
            """, (course_schedule_id, session.get('teacher_id')))

            if not cursor.fetchone():
                conn.close()
                return jsonify({"status": "error", "message": "无权限访问该课程"}), 403

            # 获取要导入的课件ID列表
            material_ids = request.form.get('material_ids', '').split(',')
            material_ids = [mid.strip() for mid in material_ids if mid.strip()]

            if not material_ids:
                conn.close()
                return jsonify({"status": "error", "message": "请选择要导入的课件"}), 400

            imported_count = 0
            current_time = datetime.now().isoformat()

            for material_id in material_ids:
                # 验证课件是否属于当前教师且在资源库中
                cursor.execute("""
                    SELECT id, title, file_path, file_type, file_size, description
                    FROM teacher_materials
                    WHERE id = ? AND teacher_id = ? AND course_schedule_id IS NULL
                """, (material_id, session.get('teacher_id')))

                material = cursor.fetchone()
                if not material:
                    continue  # 跳过无效的课件

                # 检查是否已经导入过
                cursor.execute("""
                    SELECT id FROM teacher_materials
                    WHERE teacher_id = ? AND course_schedule_id = ? AND title = ? AND file_path = ?
                """, (session.get('teacher_id'), course_schedule_id, material['title'], material['file_path']))

                if cursor.fetchone():
                    continue  # 已经导入过，跳过

                # 创建新的课件记录（关联到课程）
                new_material_id = str(uuid.uuid4())
                cursor.execute("""
                    INSERT INTO teacher_materials (id, teacher_id, title, file_path, file_type, file_size,
                                                 upload_time, created_at, folder_path, description, course_schedule_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    new_material_id,
                    session.get('teacher_id'),
                    material['title'],
                    material['file_path'],
                    material['file_type'],
                    material['file_size'],
                    current_time,
                    current_time,
                    '/',
                    material['description'],
                    course_schedule_id
                ))

                imported_count += 1

            conn.commit()
            conn.close()

            return jsonify({"status": "success", "message": f"成功导入{imported_count}个课件"})

        except Exception as e:
            return jsonify({"status": "error", "message": f"导入失败: {str(e)}"}), 500
