"""
桌面捕获相关路由
"""
from flask import jsonify, session, render_template
from app.utils.decorators import teacher_required
from app.utils.desktop_capture import desktop_capture_service


def register_routes(bp):
    """注册桌面捕获相关路由"""
    
    @bp.route('/api/desktop_capture/start', methods=['POST'])
    @teacher_required
    def start_desktop_capture():
        """开始桌面捕获"""
        try:
            result = desktop_capture_service.start_desktop_capture()
            return jsonify(result)
        except Exception as e:
            return jsonify({"status": "error", "message": f"启动失败: {str(e)}"}), 500
    
    @bp.route('/api/desktop_capture/stop', methods=['POST'])
    @teacher_required
    def stop_desktop_capture():
        """停止桌面捕获"""
        print(f"收到停止桌面捕获请求，用户ID: {session.get('teacher_id')}")
        try:
            result = desktop_capture_service.stop_desktop_capture()
            print(f"停止桌面捕获结果: {result}")
            return jsonify(result)
        except Exception as e:
            error_msg = f"停止失败: {str(e)}"
            print(f"停止桌面捕获异常: {error_msg}")
            return jsonify({"status": "error", "message": error_msg}), 500
    
    @bp.route('/api/desktop_capture/status', methods=['GET'])
    @teacher_required
    def get_desktop_capture_status():
        """获取桌面捕获状态"""
        try:
            status = desktop_capture_service.get_stream_status()
            # print(f"桌面捕获状态查询结果: {status}")
            return jsonify({"status": "success", "data": status})
        except Exception as e:
            error_msg = f"获取状态失败: {str(e)}"
            print(f"获取桌面捕获状态异常: {error_msg}")
            return jsonify({"status": "error", "message": error_msg}), 500

    @bp.route('/api/desktop_capture/force_stop', methods=['POST'])
    @teacher_required
    def force_stop_desktop_capture():
        """强制停止桌面捕获"""
        print(f"收到强制停止桌面捕获请求，用户ID: {session.get('teacher_id')}")
        try:
            # 强制清理所有相关进程
            desktop_capture_service.is_capturing = False
            desktop_capture_service._kill_ffmpeg_processes()
            desktop_capture_service.ffmpeg_process = None

            print("强制停止桌面捕获完成")
            return jsonify({"status": "success", "message": "桌面捕获已强制停止"})
        except Exception as e:
            error_msg = f"强制停止失败: {str(e)}"
            print(f"强制停止桌面捕获异常: {error_msg}")
            return jsonify({"status": "error", "message": error_msg}), 500

