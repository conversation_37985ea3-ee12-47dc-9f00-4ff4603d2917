{% extends "teacher/base.html" %}

{% block title %}课件管理{% endblock %}

{% block head %}
<style>
    .folder-item {
        transition: all 0.3s;
    }

    .folder-item:hover {
        background-color: #f0f8ff !important;
        border-color: #1E9FFF !important;
    }

    .operation-buttons {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    /* 文件夹树形结构样式 */
    .folder-item {
        transition: all 0.2s ease;
        user-select: none;
    }
    .folder-item.active {
        background-color: #1E9FFF !important;
        color: white !important;
    }
    .folder-item.active:hover {
        background-color: #1E9FFF !important;
    }
    .folder-toggle {
        transition: transform 0.2s ease;
    }
    .folder-children {
        transition: all 0.3s ease;
    }
    #folderTreeContainer {
        max-height: 400px;
        overflow-y: auto;
    }

    /* 文件列表表格样式 */
    #fileListContainer .layui-table {
        margin: 0;
    }

    #fileListContainer .layui-table th {
        background-color: #f8f8f8;
        font-weight: bold;
        text-align: center;
    }

    #fileListContainer .layui-table td {
        vertical-align: middle;
    }

    #fileListContainer .layui-table tbody tr:hover {
        background-color: #f0f8ff;
    }

    /* 操作按钮样式 */
    #fileListContainer .layui-btn-xs {
        margin-right: 3px;
    }

    .operation-buttons-container {
        text-align: center;
        padding: 15px;
        background: #f8f8f8;
        border-radius: 4px;
    }
    
    .folder-tree-container {
        padding: 15px;
        background: #fff;
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        height: 500px;
        overflow-y: auto;
    }
    
    .file-list-container {
        background: #fff;
        border: 1px solid #e6e6e6;
        border-radius: 4px;
        min-height: 500px;
    }
</style>
{% endblock %}

{% block content %}
<div class="content-card">
    <div class="layui-card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h2>课件管理</h2>
            </div>
            <div class="layui-row" style="margin-bottom: 15px;">
                <div class="layui-col-md12">
                    <div class="operation-buttons operation-buttons-container">
                        <button class="layui-btn layui-btn-normal" id="createFolderBtn">
                            <i class="layui-icon">&#xe654;</i>新建文件夹
                        </button>
                        <button class="layui-btn layui-btn-danger" id="deleteFolderBtn">
                            <i class="layui-icon">&#xe640;</i>删除文件夹
                        </button>
                        <button class="layui-btn layui-btn-warm" id="uploadBtn">
                            <i class="layui-icon">&#xe67c;</i>上传文件
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layui-card-body">
        <div class="layui-row layui-col-space15">
            <div class="layui-col-md3">
                <!-- 文件夹树表 -->
                <div class="folder-tree-container">
                    <table class="layui-hide" id="folderTreeTable"></table>
                </div>
            </div>

            <div class="layui-col-md9">
                <!-- 文件列表 -->
                <div id="fileListContainer" class="file-list-container">
                    <table class="layui-table" lay-skin="line">
                        <thead>
                            <tr>
                                <th width="50">类型</th>
                                <th>文件名</th>
                                <th width="100">文件类型</th>
                                <th width="100">文件大小</th>
                                <th width="150">描述</th>
                                <th width="200">操作</th>
                            </tr>
                        </thead>
                        <tbody id="fileTableBody">
                            {% if folders %}
                                {% for file in folders.get('/', []) %}
                                <tr>
                                    <td style="text-align: center;">
                                        <i class="layui-icon" style="font-size: 18px; color: #1E9FFF;">&#xe621;</i>
                                    </td>
                                    <td>
                                        <div style="font-weight: bold;">{{ file.title }}</div>
                                    </td>
                                    <td>
                                        <span class="layui-badge layui-bg-blue">
                                            {% if file.file_type == 'text' %}TXT
                                            {% elif file.file_type == 'word' %}WORD
                                            {% elif file.file_type == 'excel' %}EXCEL
                                            {% elif file.file_type == 'powerpoint' %}PPT
                                            {% elif file.file_type == 'pdf' %}PDF
                                            {% elif file.file_type == 'image' %}图片
                                            {% elif file.file_type == 'archive' %}压缩包
                                            {% elif file.file_type == 'media' %}媒体
                                            {% elif file.file_type == 'file' %}文件
                                            {% else %}{{ file.file_type.upper() }}
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>{{ "%.2f"|format(file.file_size / 1024 / 1024) }} MB</td>
                                    <td>
                                        {% if file.description %}
                                            <span title="{{ file.description }}">{{ file.description[:20] }}{% if file.description|length > 20 %}...{% endif %}</span>
                                        {% else %}
                                            <span style="color: #999;">无描述</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <button class="layui-btn layui-btn-xs layui-btn-normal assign-btn" data-id="{{ file.id }}">
                                            <i class="layui-icon">&#xe654;</i>分配
                                        </button>
                                        <button class="layui-btn layui-btn-xs move-btn" data-id="{{ file.id }}">
                                            <i class="layui-icon">&#xe669;</i>移动
                                        </button>
                                        <button class="layui-btn layui-btn-xs layui-btn-danger delete-btn" data-id="{{ file.id }}">
                                            <i class="layui-icon">&#xe640;</i>删除
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="6" style="text-align: center; padding: 40px; color: #999;">此文件夹为空</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 隐藏的文件输入 -->
<input type="file" id="fileInput" multiple accept=".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.txt,.zip,.rar,.jpg,.jpeg,.png,.gif" style="display: none;">

<!-- 分配课件到课堂的弹窗 -->
<div id="assignModal" style="display: none; padding: 20px;">
    <form class="layui-form" id="assignForm">
        <div class="layui-form-item">
            <label class="layui-form-label">选择课堂</label>
            <div class="layui-input-block">
                <select name="schedule_id" lay-verify="required" lay-search>
                    <option value="">请选择课堂</option>
                    {% for schedule in course_schedules %}
                    <option value="{{ schedule.id }}">
                        {{ schedule.course_name }} - {{ schedule.class_name }}
                        ({{ schedule.day_of_week }} {{ schedule.start_time }}-{{ schedule.end_time }})
                    </option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn layui-btn-normal">确认分配</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>

<!-- 新建文件夹弹窗 -->
<div id="createFolderModal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="createFolderForm">
        <div class="layui-form-item">
            <label class="layui-form-label">文件夹名称</label>
            <div class="layui-input-block">
                <input type="text" name="folder_name" placeholder="请输入文件夹名称" class="layui-input" lay-verify="required">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn layui-btn-normal" lay-submit lay-filter="createFolderForm">创建</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>

<!-- 移动文件弹窗 -->
<div id="moveFileModal" style="display: none; padding: 20px;">
    <form class="layui-form" lay-filter="moveFileForm">
        <div class="layui-form-item">
            <label class="layui-form-label">目标文件夹</label>
            <div class="layui-input-block">
                <select name="target_folder" lay-verify="required">
                    <option value="/">根目录</option>
                    <!-- 文件夹选项将通过JavaScript动态加载 -->
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn layui-btn-normal" lay-submit lay-filter="moveFileForm">移动</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
layui.use(['element', 'form', 'layer', 'table'], function(){
    var element = layui.element;
    var form = layui.form;
    var layer = layui.layer;
    var table = layui.table;

    var selectedFiles = [];
    var currentMaterialId = null;
    var currentFolder = '/';
    var currentMoveFileId = null;

    // 课件数据（从后端传递）
    var foldersData = {{ folders|tojson }};
    console.log('Folders data:', foldersData);

    // 初始化页面
    $(document).ready(function() {
        initFolderTreeTable();
        bindFileEvents();
    });

    // 获取文件类型显示名称
    function getFileTypeDisplayName(fileType) {
        switch(fileType) {
            case 'text': return 'TXT';
            case 'word': return 'WORD';
            case 'excel': return 'EXCEL';
            case 'powerpoint': return 'PPT';
            case 'pdf': return 'PDF';
            case 'image': return '图片';
            case 'archive': return '压缩包';
            case 'media': return '媒体';
            case 'file': return '文件';
            default: return fileType.toUpperCase();
        }
    }

    // 初始化文件夹树表
    function initFolderTreeTable() {
        // 构建文件夹HTML结构
        buildFolderTree();

        // 绑定文件夹点击事件
        bindFolderEvents();

        // 默认选择根目录
        selectFolder('/');
    }

    // 构建文件夹树形结构
    function buildFolderTree() {
        var folderContainer = $('#folderTreeTable').parent();
        folderContainer.html('<div id="folderTreeContainer"></div>');

        var html = '';
        var folderPaths = Object.keys(foldersData);

        // 构建树形结构数据
        var treeData = buildTreeStructure(folderPaths);

        // 渲染树形HTML
        html = renderTreeHTML(treeData, 0);

        $('#folderTreeContainer').html(html);
    }

    // 构建树形结构数据
    function buildTreeStructure(folderPaths) {
        var tree = [];
        var pathMap = {};

        // 添加根目录
        var rootNode = {
            path: '/',
            name: '根目录',
            level: 0,
            children: [],
            expanded: true
        };
        tree.push(rootNode);
        pathMap['/'] = rootNode;

        // 处理其他文件夹
        var sortedPaths = folderPaths.filter(path => path !== '/').sort();

        sortedPaths.forEach(function(folderPath) {
            var pathParts = folderPath.split('/').filter(part => part !== '');
            var parentPath = pathParts.length > 1 ? '/' + pathParts.slice(0, -1).join('/') : '/';
            var level = pathParts.length;

            var node = {
                path: folderPath,
                name: pathParts[pathParts.length - 1],
                level: level,
                children: [],
                expanded: false,
                hasChildren: sortedPaths.some(p => p !== folderPath && p.startsWith(folderPath + '/'))
            };

            pathMap[folderPath] = node;

            // 添加到父节点
            if (pathMap[parentPath]) {
                pathMap[parentPath].children.push(node);
            }
        });

        return tree;
    }

    // 渲染树形HTML
    function renderTreeHTML(nodes, level) {
        var html = '';

        nodes.forEach(function(node) {
            var indent = level * 20;
            var iconClass = node.hasChildren ?
                (node.expanded ? 'layui-icon-triangle-d' : 'layui-icon-triangle-r') :
                'layui-icon-file';
            var iconColor = node.path === '/' ? '#1E9FFF' : '#FFB800';

            html += '<div class="folder-item" data-path="' + node.path + '" data-level="' + level + '" style="padding: 8px; cursor: pointer; border-radius: 3px; margin-left: ' + indent + 'px; margin-bottom: 3px; border: 1px solid #e6e6e6;">';

            if (node.hasChildren) {
                html += '<i class="layui-icon folder-toggle ' + iconClass + '" style="color: ' + iconColor + '; margin-right: 5px; cursor: pointer;"></i>';
            } else {
                html += '<i class="layui-icon ' + iconClass + '" style="color: ' + iconColor + '; margin-right: 8px;"></i>';
            }

            html += '<span>' + node.name + '</span>';
            html += '</div>';

            // 渲染子节点
            if (node.children.length > 0) {
                html += '<div class="folder-children" data-parent="' + node.path + '" style="' + (node.expanded ? '' : 'display: none;') + '">';
                html += renderTreeHTML(node.children, level + 1);
                html += '</div>';
            }
        });

        return html;
    }

    // 绑定文件夹事件
    function bindFolderEvents() {
        // 绑定文件夹点击事件
        $(document).off('click', '.folder-item').on('click', '.folder-item', function(e) {
            e.stopPropagation();
            var folderPath = $(this).data('path');
            selectFolder(folderPath);
        });

        // 绑定展开/折叠事件
        $(document).off('click', '.folder-toggle').on('click', '.folder-toggle', function(e) {
            e.stopPropagation();
            var $this = $(this);
            var $folderItem = $this.closest('.folder-item');
            var folderPath = $folderItem.data('path');
            var $children = $folderItem.next('.folder-children[data-parent="' + folderPath + '"]');

            if ($children.length > 0) {
                if ($children.is(':visible')) {
                    // 折叠
                    $children.hide();
                    $this.removeClass('layui-icon-triangle-d').addClass('layui-icon-triangle-r');
                } else {
                    // 展开
                    $children.show();
                    $this.removeClass('layui-icon-triangle-r').addClass('layui-icon-triangle-d');
                }
            }
        });
    }

    // 选择文件夹
    function selectFolder(folderPath) {
        currentFolder = folderPath;

        // 移除所有文件夹的高亮
        $('.folder-item').removeClass('active').css({
            'background': '',
            'color': '',
            'border': '1px solid #e6e6e6'
        });

        // 高亮当前选中的文件夹
        var selectedFolder = $('.folder-item[data-path="' + folderPath + '"]');
        selectedFolder.addClass('active').css({
            'background': '#1E9FFF',
            'color': 'white',
            'border': '1px solid #1E9FFF'
        });

        // 如果是根目录，特殊处理
        if (folderPath === '/') {
            selectedFolder.css('border', 'none');
        }

        loadFileList(folderPath);
    }

    // 加载文件列表
    function loadFileList(folderPath) {
        var files = foldersData[folderPath] || [];
        var html = '';

        // 构建表格HTML
        html += '<table class="layui-table" lay-skin="line">';
        html += '<thead>';
        html += '<tr>';
        html += '<th width="50">类型</th>';
        html += '<th>文件名</th>';
        html += '<th width="100">文件类型</th>';
        html += '<th width="100">文件大小</th>';
        html += '<th width="150">描述</th>';
        html += '<th width="200">操作</th>';
        html += '</tr>';
        html += '</thead>';
        html += '<tbody>';

        if (files.length === 0) {
            html += '<tr>';
            html += '<td colspan="6" style="text-align: center; padding: 40px; color: #999;">此文件夹为空</td>';
            html += '</tr>';
        } else {
            files.forEach(function(file) {
                html += '<tr>';
                html += '<td style="text-align: center;">';
                html += '<i class="layui-icon" style="font-size: 18px; color: #1E9FFF;">&#xe621;</i>';
                html += '</td>';
                html += '<td>';
                html += '<div style="font-weight: bold;">' + file.title + '</div>';
                html += '</td>';
                html += '<td>';
                html += '<span class="layui-badge layui-bg-blue">' + getFileTypeDisplayName(file.file_type) + '</span>';
                html += '</td>';
                html += '<td>' + (file.file_size / 1024 / 1024).toFixed(2) + ' MB</td>';
                html += '<td>';
                if (file.description) {
                    var shortDesc = file.description.length > 20 ? file.description.substring(0, 20) + '...' : file.description;
                    html += '<span title="' + file.description + '">' + shortDesc + '</span>';
                } else {
                    html += '<span style="color: #999;">无描述</span>';
                }
                html += '</td>';
                html += '<td>';
                html += '<button class="layui-btn layui-btn-xs layui-btn-normal assign-btn" data-id="' + file.id + '">';
                html += '<i class="layui-icon">&#xe654;</i>分配';
                html += '</button>';
                html += '<button class="layui-btn layui-btn-xs move-btn" data-id="' + file.id + '">';
                html += '<i class="layui-icon">&#xe669;</i>移动';
                html += '</button>';
                html += '<button class="layui-btn layui-btn-xs layui-btn-danger delete-btn" data-id="' + file.id + '">';
                html += '<i class="layui-icon">&#xe640;</i>删除';
                html += '</button>';
                html += '</td>';
                html += '</tr>';
            });
        }

        html += '</tbody>';
        html += '</table>';

        $('#fileListContainer').html(html);
        bindFileEvents();
    }

    // 绑定文件事件
    function bindFileEvents() {
        $('.assign-btn').off('click').on('click', function() {
            currentMaterialId = $(this).data('id');
            layer.open({
                type: 1,
                title: '分配课件到课堂',
                content: $('#assignModal'),
                area: ['500px', '300px'],
                success: function() {
                    form.render();
                }
            });
        });

        $('.move-btn').off('click').on('click', function() {
            currentMoveFileId = $(this).data('id');
            loadFolderOptions();
            layer.open({
                type: 1,
                title: '移动文件',
                content: $('#moveFileModal'),
                area: ['400px', '250px'],
                success: function() {
                    form.render();
                }
            });
        });

        $('.delete-btn').off('click').on('click', function() {
            var materialId = $(this).data('id');
            layer.confirm('确定要删除这个课件吗？', {icon: 3, title: '提示'}, function(index) {
                $.ajax({
                    url: '/teacher/delete_material/' + materialId,
                    type: 'DELETE',
                    success: function(response) {
                        if (response.status === 'success') {
                            layer.msg(response.message, {icon: 1});
                            setTimeout(function() {
                                location.reload();
                            }, 1000);
                        } else {
                            layer.msg(response.message, {icon: 2});
                        }
                    },
                    error: function() {
                        layer.msg('删除失败，请重试', {icon: 2});
                    }
                });
                layer.close(index);
            });
        });
    }

    // 重新构建文件夹树
    function rebuildFolderTree() {
        // 重新构建文件夹HTML结构
        buildFolderTree();

        // 重新绑定事件
        bindFolderEvents();

        // 重新选择当前文件夹
        selectFolder(currentFolder);
    }

    // 加载文件夹选项
    function loadFolderOptions() {
        var select = $('select[name="target_folder"]');
        select.empty();
        select.append('<option value="/">根目录</option>');

        Object.keys(foldersData).forEach(function(folderPath) {
            if (folderPath !== '/') {
                var level = folderPath.split('/').filter(function(part) { return part !== ''; }).length;
                var indent = '　'.repeat(level); // 使用全角空格缩进
                var folderName = folderPath.split('/').pop();
                select.append('<option value="' + folderPath + '">' + indent + folderName + '</option>');
            }
        });
    }

    // 上传按钮点击事件
    $('#uploadBtn').click(function() {
        $('#fileInput').click();
    });

    // 文件选择事件
    $('#fileInput').change(function() {
        var files = this.files;
        if (files.length === 0) {
            return;
        }

        selectedFiles = Array.from(files);

        // 直接开始上传，不需要预览
        uploadFiles();
    });

    // 上传文件函数
    function uploadFiles() {
        if (selectedFiles.length === 0) {
            layer.msg('请先选择文件', {icon: 2});
            return;
        }

        var formData = new FormData();
        selectedFiles.forEach(function(file) {
            formData.append('files', file);
        });

        // 添加当前文件夹路径
        formData.append('folder_path', currentFolder);

        var loadingIndex = layer.load(2, {content: '正在上传...'});

        $.ajax({
            url: '/teacher/upload_teacher_materials',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                layer.close(loadingIndex);
                if (response.status === 'success') {
                    var message = response.message;
                    if (response.failed_files && response.failed_files.length > 0) {
                        message += '<br><br>失败的文件：<br>' + response.failed_files.join('<br>');
                        layer.alert(message, {
                            icon: 1,
                            title: '上传结果',
                            area: ['500px', 'auto']
                        });
                    } else {
                        layer.msg(message, {icon: 1});
                    }

                    // 清空选择
                    selectedFiles = [];
                    $('#fileInput').val('');
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    layer.alert(response.message, {
                        icon: 2,
                        title: '上传失败',
                        area: ['400px', 'auto']
                    });
                }
            },
            error: function(xhr, status, error) {
                layer.close(loadingIndex);
                var errorMsg = '上传失败，请重试';

                try {
                    var response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMsg = response.message;
                    }
                } catch (e) {
                    errorMsg += '（' + xhr.status + ': ' + xhr.statusText + '）';
                }

                layer.alert(errorMsg, {
                    icon: 2,
                    title: '网络错误',
                    area: ['400px', 'auto']
                });

                console.error('上传错误详情:', {
                    status: xhr.status,
                    statusText: xhr.statusText,
                    responseText: xhr.responseText,
                    error: error
                });
            }
        });
    }

    // 新建文件夹
    $('#createFolderBtn').click(function() {
        console.log('点击新建文件夹按钮');
        layer.open({
            type: 1,
            title: '新建文件夹',
            content: $('#createFolderModal'),
            area: ['400px', '250px'],
            success: function() {
                console.log('弹窗打开成功');
                form.render();
            }
        });
    });

    // 删除文件夹
    $('#deleteFolderBtn').click(function() {
        if (currentFolder === '/') {
            layer.msg('不能删除根目录', {icon: 2});
            return;
        }

        // 检查文件夹是否为空
        var files = foldersData[currentFolder] || [];
        if (files.length > 0) {
            layer.msg('文件夹不为空，无法删除', {icon: 2});
            return;
        }

        // 检查是否有子文件夹
        var hasSubfolders = Object.keys(foldersData).some(function(path) {
            return path !== currentFolder && path.startsWith(currentFolder + '/');
        });

        if (hasSubfolders) {
            layer.msg('文件夹包含子文件夹，无法删除', {icon: 2});
            return;
        }

        var folderName = currentFolder.split('/').pop();
        layer.confirm('确定要删除文件夹 "' + folderName + '" 吗？', {
            icon: 3,
            title: '删除确认'
        }, function(index) {
            $.ajax({
                url: '/teacher/delete_folder',
                type: 'DELETE',
                contentType: 'application/json',
                data: JSON.stringify({
                    folder_path: currentFolder
                }),
                success: function(response) {
                    if (response.status === 'success') {
                        layer.msg(response.message, {icon: 1});

                        // 从foldersData中移除该文件夹
                        delete foldersData[currentFolder];

                        // 重新构建文件夹树
                        rebuildFolderTree();

                        // 选择父文件夹
                        var parentPath = currentFolder.substring(0, currentFolder.lastIndexOf('/')) || '/';
                        selectFolder(parentPath);
                    } else {
                        layer.msg(response.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('删除失败，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });

    // 提交新建文件夹表单
    form.on('submit(createFolderForm)', function(data) {
        console.log('提交新建文件夹表单', data);
        console.log('当前文件夹:', currentFolder);

        var requestData = {
            folder_name: data.field.folder_name,
            parent_path: currentFolder
        };
        console.log('请求数据:', requestData);

        $.ajax({
            url: '/teacher/create_folder',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(requestData),
            success: function(response) {
                console.log('创建文件夹响应:', response);
                if (response.status === 'success') {
                    layer.msg(response.message, {icon: 1});
                    layer.closeAll();

                    // 将新文件夹添加到foldersData中
                    var newFolderPath = response.folder_path;
                    foldersData[newFolderPath] = [];

                    // 重新构建文件夹树
                    rebuildFolderTree();

                    // 选择新创建的文件夹
                    selectFolder(newFolderPath);
                } else {
                    layer.msg(response.message, {icon: 2});
                }
            },
            error: function(xhr, status, error) {
                console.log('创建文件夹错误:', xhr, status, error);
                console.log('响应文本:', xhr.responseText);
                layer.msg('创建失败，请重试', {icon: 2});
            }
        });

        return false;
    });

    // 提交分配表单
    form.on('submit(assignForm)', function(data) {
        if (!currentMaterialId) {
            layer.msg('请选择课件', {icon: 2});
            return false;
        }

        $.ajax({
            url: '/teacher/assign_material_to_class',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                material_id: currentMaterialId,
                schedule_id: data.field.schedule_id
            }),
            success: function(response) {
                if (response.status === 'success') {
                    layer.msg(response.message, {icon: 1});
                    layer.closeAll();
                } else {
                    layer.msg(response.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('分配失败，请重试', {icon: 2});
            }
        });

        return false;
    });

    // 提交移动文件表单
    form.on('submit(moveFileForm)', function(data) {
        if (!currentMoveFileId) {
            layer.msg('请选择文件', {icon: 2});
            return false;
        }

        $.ajax({
            url: '/dashboard/move_material',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                material_id: currentMoveFileId,
                folder_path: data.field.target_folder
            }),
            success: function(response) {
                if (response.status === 'success') {
                    layer.msg(response.message, {icon: 1});
                    layer.closeAll();
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg(response.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('移动失败，请重试', {icon: 2});
            }
        });

        return false;
    });

    // 初始化表单
    form.render();
});
</script>
{% endblock %}
