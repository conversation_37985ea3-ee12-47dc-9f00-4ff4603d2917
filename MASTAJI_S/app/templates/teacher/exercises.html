{% extends "teacher/base.html" %}

{% block title %}习题管理{% endblock %}

{%block styles%}

<style>
.exercise-item {
    background: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 6px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.exercise-item:hover {
    border-color: #1E9FFF;
    box-shadow: 0 2px 8px rgba(30, 159, 255, 0.1);
}

.exercise-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
}

.exercise-meta {
    display: flex;
    gap: 10px;
    align-items: center;
}

.exercise-type, .exercise-difficulty, .exercise-subject {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    color: white;
}

.type-single { background: #1E9FFF; }
.type-multiple { background: #FF5722; }
.type-judge { background: #009688; }
.type-fill { background: #FF9800; }
.type-essay { background: #9C27B0; }

.difficulty-easy { background: #4CAF50; }
.difficulty-medium { background: #FF9800; }
.difficulty-hard { background: #F44336; }

.exercise-subject {
    background: #666;
}

.exercise-content {
    padding: 20px;
}

.exercise-question {
    margin-bottom: 15px;
    font-size: 15px;
    line-height: 1.6;
}

.exercise-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    margin-left: 20px;
}

.option-item {
    padding: 8px 12px;
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 14px;
}

.option-item.correct {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.exercise-footer {
    padding: 10px 20px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    color: #666;
}

/* 习题弹窗选项样式 */
.exercise-option-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    padding: 8px 12px;
    border-radius: 6px;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.exercise-option-item:hover {
    background-color: #f8f9fa;
}

.exercise-option-item.correct {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
}

.exercise-option-content {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 10px;
}

.exercise-option-letter {
    font-weight: bold;
    color: #666;
    min-width: 20px;
}

.exercise-option-text {
    flex: 1;
    border: none;
    background: transparent;
}

.exercise-option-text:focus {
    border: 1px solid #1E9FFF;
    background: #fff;
}

.exercise-option-checkbox {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    position: absolute !important;
    left: -9999px !important;
}

/* 更强的选择器确保隐藏 */
#optionsList .exercise-option-checkbox,
#optionsList input[type="radio"],
#optionsList input[type="checkbox"] {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    width: 0 !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    position: absolute !important;
    left: -9999px !important;
}

/* 隐藏默认生成的layui选框 */
#optionsList .layui-form-checkbox,
#optionsList .layui-form-radio {
    display: none !important;
}

.exercise-correct-answer {
    background: #52c41a;
    color: white;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 12px;
    margin-left: 10px;
}

.exercise-delete-btn {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #ff4d4f;
    color: white;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    margin-left: 10px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.exercise-option-item:hover .exercise-delete-btn {
    opacity: 1;
}

.exercise-delete-btn:hover {
    background: #ff7875;
}

.judge-options {
    display: flex;
    gap: 20px;
}

.judge-option {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 表格中的标签样式 */
.layui-badge.type-single { background: #1E9FFF; }
.layui-badge.type-multiple { background: #FF5722; }
.layui-badge.type-judge { background: #009688; }
.layui-badge.type-fill { background: #FF9800; }
.layui-badge.type-essay { background: #9C27B0; }

.layui-badge.difficulty-easy { background: #4CAF50; }
.layui-badge.difficulty-medium { background: #FF9800; }
.layui-badge.difficulty-hard { background: #F44336; }

/* 搜索筛选容器样式 */
.search-filter-container {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 20px;
    padding: 16px;
    background: #f8f9fa;
    border-radius: 8px;
}

/* 搜索框样式 */
.search-box {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
    max-width: 450px;
}

.search-input-wrapper {
    flex: 1;
}

.search-box .layui-input-group {
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #d9d9d9;
    background: #fff;
    display: flex;
    align-items: center;
}

.search-box .layui-input {
    border: none;
    padding: 8px 12px;
    font-size: 14px;
    flex: 1;
    outline: none;
}

.search-box .layui-input-prefix {
    padding: 0 12px;
    background: #fff;
    border: none;
    display: flex;
    align-items: center;
}

.search-box .layui-input-suffix {
    display: flex;
    align-items: center;
    padding: 0 8px;
    background: #fff;
}

.clear-btn {
    background: none;
    border: none;
    color: #999;
    padding: 4px;
    cursor: pointer;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-btn:hover {
    background: #f0f0f0;
    color: #666;
}

.search-btn {
    background: #1890ff;
    color: #fff;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    white-space: nowrap;
    height: 36px;
    display: flex;
    align-items: center;
}

.search-btn:hover {
    background: #40a9ff;
}

/* 筛选按钮样式 */
.filter-box {
    position: relative;
}

.filter-dropdown {
    position: relative;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    min-width: 120px;
}

.filter-btn:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.filter-btn .arrow-icon {
    margin-left: auto;
    transition: transform 0.3s;
}

.filter-btn.active .arrow-icon {
    transform: rotate(180deg);
}

/* 筛选面板样式 */
.filter-panel {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    margin-top: 4px;
    padding: 16px;
    min-width: 300px;
}

.filter-section {
    margin-bottom: 16px;
}

.filter-section:last-of-type {
    margin-bottom: 0;
}

.filter-label {
    font-size: 14px;
    color: #333;
    margin-bottom: 8px;
    font-weight: 500;
}

.filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.filter-option {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    padding: 4px 0;
}

.filter-option input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
}

.filter-option:hover {
    color: #1890ff;
}

/* 筛选操作按钮 */
.filter-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #f0f0f0;
}

.clear-filter-btn {
    background: none;
    border: 1px solid #d9d9d9;
    color: #666;
    padding: 6px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.clear-filter-btn:hover {
    border-color: #1890ff;
    color: #1890ff;
}

.confirm-filter-btn {
    background: #1890ff;
    color: #fff;
    border: none;
    padding: 6px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.confirm-filter-btn:hover {
    background: #40a9ff;
}

/* 题目统计样式 */
.question-count {
    color: #666;
    font-size: 14px;
    white-space: nowrap;
}
</style>

{% endblock %}

{% block content %}
<div class="content-card">
    <div class="layui-card-header">
        <div style="display: flex; justify-content: space-between; align-items: center;">
            <div>
                <h2>习题管理</h2>
            </div>
            <div>
                <button type="button" class="layui-btn layui-btn-normal" id="addExerciseBtn">
                    <i class="layui-icon layui-icon-add-1"></i> 新建习题
                </button>
            </div>
        </div>
    </div>
    <div class="layui-card-body">
        <!-- 搜索和筛选区域 -->
        <div class="search-filter-container">
            <!-- 搜索框 -->
            <div class="search-box">
                <div class="search-input-wrapper">
                    <div class="layui-input-group">
                        <div class="layui-input-prefix">
                            <i class="layui-icon layui-icon-search"></i>
                        </div>
                        <input type="text" id="searchInput" placeholder="搜索内容" class="layui-input">
                        <div class="layui-input-suffix">
                            <button type="button" id="clearSearchBtn" class="clear-btn" style="display: none;">
                                <i class="layui-icon layui-icon-close"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <button type="button" id="searchBtn" class="search-btn">搜索</button>
            </div>

            <!-- 筛选按钮 -->
            <div class="filter-box">
                <div class="filter-dropdown">
                    <button type="button" class="filter-btn" id="filterBtn">
                        <i class="layui-icon layui-icon-screen"></i>
                        <span>筛选</span>
                        <span id="filterText">全部</span>
                        <i class="layui-icon layui-icon-down arrow-icon"></i>
                    </button>

                    <!-- 筛选下拉面板 -->
                    <div class="filter-panel" id="filterPanel" style="display: none;">
                        <!-- 题型筛选 -->
                        <div class="filter-section">
                            <div class="filter-label">题型：</div>
                            <div class="filter-options">
                                <label class="filter-option">
                                    <input type="checkbox" name="typeFilter" value="single">
                                    <span>单选题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="typeFilter" value="multiple">
                                    <span>多选题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="typeFilter" value="judge">
                                    <span>判断题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="typeFilter" value="fill">
                                    <span>填空题</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="typeFilter" value="essay">
                                    <span>主观题</span>
                                </label>
                            </div>
                        </div>

                        <!-- 难度筛选 -->
                        <div class="filter-section">
                            <div class="filter-label">难度：</div>
                            <div class="filter-options">
                                <label class="filter-option">
                                    <input type="checkbox" name="difficultyFilter" value="easy">
                                    <span>1级</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="difficultyFilter" value="medium">
                                    <span>2级</span>
                                </label>
                                <label class="filter-option">
                                    <input type="checkbox" name="difficultyFilter" value="hard">
                                    <span>3级</span>
                                </label>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="filter-actions">
                            <button type="button" class="clear-filter-btn" id="clearFilterBtn">清空条件</button>
                            <button type="button" class="confirm-filter-btn" id="confirmFilterBtn">确定</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 题目统计 -->
            <div class="question-count">
                <span id="questionCountText">共 0 道题</span>
            </div>
        </div>

        <!-- 习题列表 -->
        <div class="exercise-list" id="exerciseList">
            <table class="layui-table" lay-skin="line">
                <thead>
                    <tr>
                        <th width="80">序号</th>
                        <th width="100">题型</th>
                        <th>题目内容</th>
                        <th width="80">难度</th>
                        <th width="120">创建时间</th>
                        <th width="180">操作</th>
                    </tr>
                </thead>
                <tbody id="exerciseTableBody">
                    <tr>
                        <td colspan="6" style="text-align: center; color: #999; padding: 40px;">
                            暂无习题数据，点击"新建习题"开始创建
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div id="pagination" style="text-align: center; margin-top: 30px;"></div>
    </div>
</div>

<!-- 新建/编辑习题弹窗 -->
<div id="exerciseModal" style="display: none; padding: 20px;">
    <form class="layui-form" id="exerciseForm">
        <div class="layui-form-item">
            <label class="layui-form-label">题目类型</label>
            <div class="layui-input-block">
                <select name="type" lay-filter="exerciseType" lay-verify="required">
                    <option value="">请选择题目类型</option>
                    <option value="single" selected>单选题</option>
                    <option value="multiple">多选题</option>
                    <option value="judge">判断题</option>
                    <option value="fill">填空题</option>
                    <option value="essay">简答题</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">题目内容</label>
            <div class="layui-input-block">
                <textarea name="question" placeholder="请输入题目内容..." class="layui-textarea" lay-verify="required"></textarea>
            </div>
        </div>

        <!-- 选择题选项容器 -->
        <div class="layui-form-item" id="optionsContainer" style="display: none;">
            <label class="layui-form-label">选项设置</label>
            <div class="layui-input-block">
                <div id="optionsList">
                    <!-- 选项将通过JavaScript动态生成 -->
                </div>
                <div class="add-option-btn" style="margin-top: 10px;">
                    <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="addExerciseOption()">
                        <i class="layui-icon layui-icon-add-1"></i> 添加选项
                    </button>
                </div>
            </div>
        </div>

        <!-- 判断题选项容器 -->
        <div class="layui-form-item" id="judgeContainer" style="display: none;">
            <label class="layui-form-label">正确答案</label>
            <div class="layui-input-block">
                <div class="judge-options">
                    <div class="judge-option" data-value="正确">
                        <input type="radio" name="judgeAnswer" value="正确" title="正确" checked>
                    </div>
                    <div class="judge-option" data-value="错误">
                        <input type="radio" name="judgeAnswer" value="错误" title="错误">
                    </div>
                </div>
            </div>
        </div>

        <!-- 填空题答案容器 -->
        <div class="layui-form-item" id="fillContainer" style="display: none;">
            <label class="layui-form-label">参考答案</label>
            <div class="layui-input-block">
                <input type="text" name="fillAnswer" placeholder="请输入填空题参考答案" class="layui-input">
            </div>
        </div>

        <!-- 简答题答案容器 -->
        <div class="layui-form-item" id="essayContainer" style="display: none;">
            <label class="layui-form-label">参考答案</label>
            <div class="layui-input-block">
                <textarea name="essayAnswer" placeholder="请输入简答题参考答案" class="layui-textarea" rows="4"></textarea>
            </div>
        </div>

        <div class="layui-form-item">
            <label class="layui-form-label">难度等级</label>
            <div class="layui-input-block">
                <select name="difficulty" lay-verify="required">
                    <option value="">请选择难度</option>
                    <option value="easy">简单</option>
                    <option value="medium">中等</option>
                    <option value="hard">困难</option>
                </select>
            </div>
        </div>

        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit lay-filter="exerciseSubmit">保存</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>

{% endblock %}

{% block scripts %}
<script>
layui.use(['form', 'layer', 'laypage'], function(){
    var form = layui.form;
    var layer = layui.layer;
    var laypage = layui.laypage;

    // 全局变量存储当前筛选条件
    var currentFilters = {
        keyword: '',
        types: [],
        difficulties: []
    };

    // 新建习题
    $('#addExerciseBtn').click(function() {
        openExerciseModal();
    });

    // 加载习题列表
    function loadExercises() {
        loadExercisesWithFilters();
    }

    // 带筛选条件加载习题列表
    function loadExercisesWithFilters() {
        var params = {};

        // 添加搜索关键词
        if (currentFilters.keyword) {
            params.keyword = currentFilters.keyword;
        }

        // 添加题型筛选
        if (currentFilters.types.length > 0) {
            params.types = currentFilters.types.join(',');
        }

        // 添加难度筛选
        if (currentFilters.difficulties.length > 0) {
            params.difficulties = currentFilters.difficulties.join(',');
        }

        $.ajax({
            url: '/teacher/get_exercises',
            type: 'GET',
            data: params,
            success: function(response) {
                if (response.status === 'success') {
                    renderExerciseTable(response.exercises);
                } else {
                    layer.msg('加载习题失败：' + response.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    }

    // 渲染习题表格
    function renderExerciseTable(exercises) {
        var tbody = $('#exerciseTableBody');
        var html = '';

        // 更新题目统计
        updateQuestionCount(exercises.length);

        if (exercises.length === 0) {
            html = '<tr><td colspan="6" style="text-align: center; color: #999; padding: 40px;">暂无习题数据，点击"新建习题"开始创建</td></tr>';
        } else {
            exercises.forEach(function(exercise, index) {
                var typeText = getTypeText(exercise.type);
                var difficultyText = getDifficultyText(exercise.difficulty);
                var createTime = formatDateTime(exercise.created_at);
                var questionPreview = exercise.question.length > 30 ? exercise.question.substring(0, 30) + '...' : exercise.question;

                html += '<tr>';
                html += '<td>' + (index + 1) + '</td>';
                html += '<td><span class="layui-badge type-' + exercise.type + '">' + typeText + '</span></td>';
                html += '<td title="' + exercise.question + '">' + questionPreview + '</td>';
                html += '<td><span class="layui-badge difficulty-' + exercise.difficulty + '">' + difficultyText + '</span></td>';
                html += '<td>' + createTime + '</td>';
                html += '<td>';
                html += '<button class="layui-btn layui-btn-xs layui-btn-normal" onclick="editExercise(\'' + exercise.id + '\')">编辑</button> ';
                html += '<button class="layui-btn layui-btn-xs layui-btn-danger" onclick="deleteExercise(\'' + exercise.id + '\')">删除</button>';
                html += '</td>';
                html += '</tr>';
            });
        }

        tbody.html(html);
    }

    // 更新题目统计
    function updateQuestionCount(count) {
        $('#questionCountText').text('共 ' + count + ' 道题');
    }

    // 获取题型文本
    function getTypeText(type) {
        var typeMap = {
            'single': '单选题',
            'multiple': '多选题',
            'judge': '判断题',
            'fill': '填空题',
            'essay': '简答题'
        };
        return typeMap[type] || type;
    }

    // 获取难度文本
    function getDifficultyText(difficulty) {
        var difficultyMap = {
            'easy': '简单',
            'medium': '中等',
            'hard': '困难'
        };
        return difficultyMap[difficulty] || difficulty;
    }

    // 格式化日期时间
    function formatDateTime(dateTimeStr) {
        var date = new Date(dateTimeStr);
        return date.getFullYear() + '-' +
               String(date.getMonth() + 1).padStart(2, '0') + '-' +
               String(date.getDate()).padStart(2, '0') + ' ' +
               String(date.getHours()).padStart(2, '0') + ':' +
               String(date.getMinutes()).padStart(2, '0');
    }

    // 页面加载时获取习题列表
    loadExercises();

    // 打开习题编辑弹窗
    function openExerciseModal(exerciseData = null) {
        layer.open({
            type: 1,
            title: exerciseData ? '编辑习题' : '新建习题',
            content: $('#exerciseModal'),
            area: ['50%', '80%'],
            success: function() {
                // 重置表单
                $('#exerciseForm')[0].reset();

                // 隐藏所有容器
                $('#optionsContainer').hide();
                $('#judgeContainer').hide();
                $('#fillContainer').hide();
                $('#essayContainer').hide();

                // 设置编辑模式标识
                if (exerciseData) {
                    $('#exerciseForm').attr('data-edit-id', exerciseData.id);
                    // 填充编辑数据
                    fillExerciseForm(exerciseData);
                } else {
                    $('#exerciseForm').removeAttr('data-edit-id');
                    // 新建习题时，设置默认值并显示单选题选项
                    $('select[name="type"]').val('single');
                    $('#optionsContainer').show();
                    generateOptions('single');
                }

                // 重新渲染表单
                form.render();

                // 确保选框被隐藏
                setTimeout(function() {
                    forceHideCheckboxes();
                }, 100);
            }
        });
    }

    // 填充编辑表单数据
    function fillExerciseForm(exerciseData) {
        // 填充基本信息
        $('select[name="type"]').val(exerciseData.type);
        $('textarea[name="question"]').val(exerciseData.question);
        $('select[name="difficulty"]').val(exerciseData.difficulty);

        // 根据题型显示对应容器并填充数据
        if (exerciseData.type === 'single' || exerciseData.type === 'multiple') {
            $('#optionsContainer').show();
            fillOptionsData(exerciseData);
        } else if (exerciseData.type === 'judge') {
            $('#judgeContainer').show();
            $('input[name="judgeAnswer"][value="' + exerciseData.answer + '"]').prop('checked', true);
        } else if (exerciseData.type === 'fill') {
            $('#fillContainer').show();
            $('input[name="fillAnswer"]').val(exerciseData.answer);
        } else if (exerciseData.type === 'essay') {
            $('#essayContainer').show();
            $('textarea[name="essayAnswer"]').val(exerciseData.answer);
        }
    }

    // 填充选择题选项数据
    function fillOptionsData(exerciseData) {
        var optionsList = $('#optionsList');
        var html = '';
        var options = exerciseData.options || [];
        var correctAnswers = [];

        // 解析正确答案
        if (exerciseData.type === 'single') {
            correctAnswers = [exerciseData.answer];
        } else if (exerciseData.type === 'multiple') {
            correctAnswers = exerciseData.answer.split(',');
        }

        // 生成选项HTML
        for (var i = 0; i < options.length; i++) {
            var optionLetter = String.fromCharCode(65 + i);
            var isCorrect = correctAnswers.includes(optionLetter);
            var inputType = exerciseData.type === 'single' ? 'radio' : 'checkbox';

            html += '<div class="exercise-option-item' + (isCorrect ? ' correct' : '') + '" data-index="' + i + '">';
            html += '<div class="exercise-option-content">';
            html += '<input type="' + inputType + '" name="exercise_option" class="exercise-option-checkbox" value="' + optionLetter + '" ' + (isCorrect ? 'checked' : '') + ' style="display: none !important;">';
            html += '<span class="exercise-option-letter">' + optionLetter + '.</span>';
            html += '<input type="text" class="layui-input exercise-option-text" placeholder="请输入选项内容" value="' + options[i] + '">';
            if (isCorrect) {
                html += '<span class="exercise-correct-answer">正确答案</span>';
            }
            html += '</div>';
            html += '<button type="button" class="exercise-delete-btn" onclick="deleteExerciseOption(' + i + ')" title="删除选项">×</button>';
            html += '</div>';
        }

        optionsList.html(html);

        // 强制隐藏所有选框
        forceHideCheckboxes();

        // 为选项添加事件监听
        bindOptionEvents(exerciseData.type);
    }

    // 题目类型变化事件
    form.on('select(exerciseType)', function(data) {
        var type = data.value;

        // 隐藏所有容器
        $('#optionsContainer').hide();
        $('#judgeContainer').hide();
        $('#fillContainer').hide();
        $('#essayContainer').hide();

        // 根据类型显示对应容器
        if (type === 'single' || type === 'multiple') {
            $('#optionsContainer').show();
            generateOptions(type);
        } else if (type === 'judge') {
            $('#judgeContainer').show();
        } else if (type === 'fill') {
            $('#fillContainer').show();
        } else if (type === 'essay') {
            $('#essayContainer').show();
        }

        form.render();

        // 确保选框被隐藏
        setTimeout(function() {
            forceHideCheckboxes();
        }, 100);
    });

    // 生成选项
    function generateOptions(type) {
        var optionsList = $('#optionsList');
        var html = '';
        var letters = ['A', 'B', 'C', 'D'];

        for (var i = 0; i < 4; i++) { // 默认生成4个选项
            var isFirst = i === 0;
            var inputType = type === 'single' ? 'radio' : 'checkbox';

            html += '<div class="exercise-option-item' + (isFirst ? ' correct' : '') + '" data-index="' + i + '">';
            html += '<div class="exercise-option-content">';
            html += '<input type="' + inputType + '" name="exercise_option" class="exercise-option-checkbox" value="' + letters[i] + '" ' + (isFirst ? 'checked' : '') + ' style="display: none !important;">';
            html += '<span class="exercise-option-letter">' + letters[i] + '.</span>';
            html += '<input type="text" class="layui-input exercise-option-text" placeholder="请输入选项内容">';
            if (isFirst) {
                html += '<span class="exercise-correct-answer">正确答案</span>';
            }
            html += '</div>';
            html += '<button type="button" class="exercise-delete-btn" onclick="deleteExerciseOption(' + i + ')" title="删除选项">×</button>';
            html += '</div>';
        }

        optionsList.html(html);

        // 强制隐藏所有选框
        forceHideCheckboxes();

        // 为选项添加事件监听
        bindOptionEvents(type);
    }

    // 强制隐藏所有选框的函数
    function forceHideCheckboxes() {
        // 使用多种方式确保选框被隐藏
        $('#optionsList input[type="radio"], #optionsList input[type="checkbox"], #optionsList .exercise-option-checkbox').each(function() {
            $(this).hide();
            $(this).css({
                'display': 'none !important',
                'visibility': 'hidden !important',
                'opacity': '0 !important',
                'width': '0 !important',
                'height': '0 !important',
                'margin': '0 !important',
                'padding': '0 !important',
                'position': 'absolute !important',
                'left': '-9999px !important'
            });
            $(this).attr('style', 'display: none !important; visibility: hidden !important; opacity: 0 !important; width: 0 !important; height: 0 !important; margin: 0 !important; padding: 0 !important; position: absolute !important; left: -9999px !important;');
        });
    }

    // 绑定选项事件
    function bindOptionEvents(type) {
        $('#optionsList .exercise-option-item').each(function() {
            var $item = $(this);
            var $checkbox = $item.find('.exercise-option-checkbox');

            // 绑定整个选项项的点击事件
            $item.off('click').on('click', function(e) {
                // 如果点击的是删除按钮或文本输入框，不处理
                if ($(e.target).hasClass('exercise-delete-btn') ||
                    $(e.target).closest('.exercise-delete-btn').length ||
                    $(e.target).hasClass('exercise-option-text')) {
                    return;
                }

                // 阻止事件冒泡
                e.preventDefault();
                e.stopPropagation();

                if (type === 'single') {
                    // 单选：移除所有正确答案标识
                    $('#optionsList .exercise-option-item').each(function() {
                        $(this).removeClass('correct');
                        $(this).find('.exercise-option-checkbox').prop('checked', false);
                        $(this).find('.exercise-correct-answer').remove();
                    });

                    // 设置当前为正确答案
                    $checkbox.prop('checked', true);
                    $item.addClass('correct');
                    if (!$item.find('.exercise-correct-answer').length) {
                        $item.find('.exercise-option-content').append('<span class="exercise-correct-answer">正确答案</span>');
                    }
                } else if (type === 'multiple') {
                    // 多选：切换选中状态
                    var isChecked = $checkbox.prop('checked');
                    $checkbox.prop('checked', !isChecked);

                    if (!isChecked) {
                        $item.addClass('correct');
                        if (!$item.find('.exercise-correct-answer').length) {
                            $item.find('.exercise-option-content').append('<span class="exercise-correct-answer">正确答案</span>');
                        }
                    } else {
                        $item.removeClass('correct');
                        $item.find('.exercise-correct-answer').remove();
                    }
                }
            });

            // 阻止文本框的点击事件冒泡
            var $optionText = $item.find('.exercise-option-text');
            if ($optionText.length) {
                $optionText.off('click').on('click', function(e) {
                    e.stopPropagation();
                });
            }
        });
    }

    // 添加选项
    window.addExerciseOption = function() {
        var optionsList = $('#optionsList');
        var options = optionsList.find('.exercise-option-item');
        var newIndex = options.length;
        var optionLetter = String.fromCharCode(65 + newIndex);
        var type = $('select[name="type"]').val();
        var inputType = type === 'single' ? 'radio' : 'checkbox';

        var newOptionHtml = '<div class="exercise-option-item" data-index="' + newIndex + '">';
        newOptionHtml += '<div class="exercise-option-content">';
        newOptionHtml += '<input type="' + inputType + '" name="exercise_option" class="exercise-option-checkbox" value="' + optionLetter + '" style="display: none !important;">';
        newOptionHtml += '<span class="exercise-option-letter">' + optionLetter + '.</span>';
        newOptionHtml += '<input type="text" class="layui-input exercise-option-text" placeholder="请输入选项内容">';
        newOptionHtml += '</div>';
        newOptionHtml += '<button type="button" class="exercise-delete-btn" onclick="deleteExerciseOption(' + newIndex + ')" title="删除选项">×</button>';
        newOptionHtml += '</div>';

        var $newOption = $(newOptionHtml);
        optionsList.append($newOption);

        // 强制隐藏新添加选项的选框
        forceHideCheckboxes();

        // 为新选项绑定事件
        var $checkbox = $newOption.find('.exercise-option-checkbox');

        // 绑定整个选项项的点击事件
        $newOption.on('click', function(e) {
            // 如果点击的是删除按钮或文本输入框，不处理
            if ($(e.target).hasClass('exercise-delete-btn') ||
                $(e.target).closest('.exercise-delete-btn').length ||
                $(e.target).hasClass('exercise-option-text')) {
                return;
            }

            // 阻止事件冒泡
            e.preventDefault();
            e.stopPropagation();

            if (type === 'single') {
                // 单选：移除所有正确答案标识
                $('#optionsList .exercise-option-item').each(function() {
                    $(this).removeClass('correct');
                    $(this).find('.exercise-option-checkbox').prop('checked', false);
                    $(this).find('.exercise-correct-answer').remove();
                });

                // 设置当前为正确答案
                $checkbox.prop('checked', true);
                $newOption.addClass('correct');
                if (!$newOption.find('.exercise-correct-answer').length) {
                    $newOption.find('.exercise-option-content').append('<span class="exercise-correct-answer">正确答案</span>');
                }
            } else if (type === 'multiple') {
                // 多选：切换选中状态
                var isChecked = $checkbox.prop('checked');
                $checkbox.prop('checked', !isChecked);

                if (!isChecked) {
                    $newOption.addClass('correct');
                    if (!$newOption.find('.exercise-correct-answer').length) {
                        $newOption.find('.exercise-option-content').append('<span class="exercise-correct-answer">正确答案</span>');
                    }
                } else {
                    $newOption.removeClass('correct');
                    $newOption.find('.exercise-correct-answer').remove();
                }
            }
        });

        // 阻止文本框的点击事件冒泡
        var $optionText = $newOption.find('.exercise-option-text');
        if ($optionText.length) {
            $optionText.on('click', function(e) {
                e.stopPropagation();
            });
        }
    };

    // 删除选项
    window.deleteExerciseOption = function(index) {
        var optionsList = $('#optionsList');
        var options = optionsList.find('.exercise-option-item');

        if (options.length <= 2) {
            layer.msg('至少需要保留2个选项', {icon: 2});
            return;
        }

        // 删除指定选项
        options.eq(index).remove();

        // 重新排列选项字母标识和索引
        var remainingOptions = optionsList.find('.exercise-option-item');
        remainingOptions.each(function(i) {
            var $option = $(this);
            var optionLetter = String.fromCharCode(65 + i);

            // 更新data-index
            $option.attr('data-index', i);

            // 更新选项字母
            $option.find('.exercise-option-letter').text(optionLetter + '.');

            // 更新checkbox的value
            $option.find('.exercise-option-checkbox').val(optionLetter);

            // 更新删除按钮的onclick事件
            $option.find('.exercise-delete-btn').attr('onclick', 'deleteExerciseOption(' + i + ')');
        });

        // 强制隐藏所有选框
        forceHideCheckboxes();

        // 重新绑定事件
        var type = $('select[name="type"]').val();
        bindOptionEvents(type);
    };

    // 编辑习题
    window.editExercise = function(id) {
        // 获取习题详情
        var loading = layer.load(1, {shade: [0.1, '#fff']});

        $.ajax({
            url: '/teacher/get_exercise/' + id,
            type: 'GET',
            success: function(response) {
                layer.close(loading);
                if (response.status === 'success') {
                    openExerciseModal(response.exercise);
                } else {
                    layer.msg('获取习题详情失败：' + response.message, {icon: 2});
                }
            },
            error: function() {
                layer.close(loading);
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
    };

    // 删除习题
    window.deleteExercise = function(id) {
        layer.confirm('确定要删除这道习题吗？删除后无法恢复！', {icon: 3, title: '确认删除'}, function(index) {
            var loading = layer.load(1, {shade: [0.1, '#fff']});

            $.ajax({
                url: '/teacher/delete_exercise/' + id,
                type: 'DELETE',
                success: function(response) {
                    layer.close(loading);
                    if (response.status === 'success') {
                        layer.msg('习题删除成功！', {icon: 1});
                        // 重新加载习题列表
                        loadExercises();
                    } else {
                        layer.msg('删除失败：' + response.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.close(loading);
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });

            layer.close(index);
        });
    };

    // 表单提交
    form.on('submit(exerciseSubmit)', function(data) {
        var formData = data.field;
        var type = formData.type;
        var question = formData.question;
        var difficulty = formData.difficulty;

        // 验证基本字段
        if (!type || !question || !difficulty) {
            layer.msg('请填写完整的习题信息', {icon: 2});
            return false;
        }

        var exerciseData = {
            type: type,
            question: question,
            difficulty: difficulty,
            options: [],
            answer: ''
        };

        // 根据题目类型收集答案和选项
        if (type === 'single' || type === 'multiple') {
            var optionItems = $('#optionsList .exercise-option-item');
            var correctAnswers = [];

            optionItems.each(function(index) {
                var optionText = $(this).find('.exercise-option-text').val();
                if (!optionText.trim()) {
                    layer.msg('请填写所有选项内容', {icon: 2});
                    return false;
                }
                exerciseData.options.push(optionText);

                if ($(this).find('.exercise-option-checkbox').prop('checked')) {
                    correctAnswers.push(String.fromCharCode(65 + index));
                }
            });

            if (correctAnswers.length === 0) {
                layer.msg('请至少选择一个正确答案', {icon: 2});
                return false;
            }

            exerciseData.answer = type === 'single' ? correctAnswers[0] : correctAnswers.join(',');
        } else if (type === 'judge') {
            exerciseData.answer = $('input[name="judgeAnswer"]:checked').val() || '正确';
            exerciseData.options = ['正确', '错误'];
        } else if (type === 'fill') {
            exerciseData.answer = formData.fillAnswer || '';
            if (!exerciseData.answer.trim()) {
                layer.msg('请填写填空题参考答案', {icon: 2});
                return false;
            }
        } else if (type === 'essay') {
            exerciseData.answer = formData.essayAnswer || '';
            if (!exerciseData.answer.trim()) {
                layer.msg('请填写简答题参考答案', {icon: 2});
                return false;
            }
        }

        // 检查是否为编辑模式
        var editId = $('#exerciseForm').attr('data-edit-id');
        var isEdit = !!editId;

        // 提交数据
        var loading = layer.load(1, {shade: [0.1, '#fff']});

        var ajaxConfig = {
            contentType: 'application/json',
            data: JSON.stringify(exerciseData),
            success: function(response) {
                layer.close(loading);
                if (response.status === 'success') {
                    layer.msg(isEdit ? '习题更新成功！' : '习题保存成功！', {icon: 1});
                    layer.closeAll();
                    // 重新加载习题列表
                    loadExercises();
                } else {
                    layer.msg((isEdit ? '更新' : '保存') + '失败：' + response.message, {icon: 2});
                }
            },
            error: function() {
                layer.close(loading);
                layer.msg('网络错误，请重试', {icon: 2});
            }
        };

        if (isEdit) {
            // 编辑模式：使用PUT请求更新
            ajaxConfig.url = '/teacher/update_exercise/' + editId;
            ajaxConfig.type = 'PUT';
        } else {
            // 新建模式：使用POST请求保存
            ajaxConfig.url = '/teacher/save_exercise';
            ajaxConfig.type = 'POST';
        }

        $.ajax(ajaxConfig);

        return false;
    });

    // 搜索框输入监听
    $('#searchInput').on('input', function() {
        var keyword = $(this).val().trim();
        if (keyword) {
            $('#clearSearchBtn').show();
        } else {
            $('#clearSearchBtn').hide();
        }
    });

    // 搜索按钮点击
    $('#searchBtn').click(function() {
        performSearch();
    });

    // 搜索框回车键
    $('#searchInput').keypress(function(e) {
        if (e.which === 13) {
            performSearch();
        }
    });

    // 清除搜索按钮
    $('#clearSearchBtn').click(function() {
        $('#searchInput').val('');
        $(this).hide();
        currentFilters.keyword = '';
        loadExercisesWithFilters();
    });

    // 执行搜索
    function performSearch() {
        var keyword = $('#searchInput').val().trim();
        currentFilters.keyword = keyword;
        loadExercisesWithFilters();
    }

    // 筛选按钮点击
    $('#filterBtn').click(function(e) {
        e.stopPropagation();
        var $panel = $('#filterPanel');
        var $btn = $(this);

        if ($panel.is(':visible')) {
            $panel.hide();
            $btn.removeClass('active');
        } else {
            $panel.show();
            $btn.addClass('active');
        }
    });

    // 点击其他地方关闭筛选面板
    $(document).click(function() {
        $('#filterPanel').hide();
        $('#filterBtn').removeClass('active');
    });

    // 阻止筛选面板内部点击事件冒泡
    $('#filterPanel').click(function(e) {
        e.stopPropagation();
    });

    // 确定筛选按钮
    $('#confirmFilterBtn').click(function() {
        // 收集题型筛选
        var selectedTypes = [];
        $('input[name="typeFilter"]:checked').each(function() {
            selectedTypes.push($(this).val());
        });
        currentFilters.types = selectedTypes;

        // 收集难度筛选
        var selectedDifficulties = [];
        $('input[name="difficultyFilter"]:checked').each(function() {
            selectedDifficulties.push($(this).val());
        });
        currentFilters.difficulties = selectedDifficulties;

        // 更新筛选按钮文本
        updateFilterText();

        // 关闭筛选面板
        $('#filterPanel').hide();
        $('#filterBtn').removeClass('active');

        // 执行筛选
        loadExercisesWithFilters();
    });

    // 清空筛选条件按钮
    $('#clearFilterBtn').click(function() {
        // 清空所有复选框
        $('input[name="typeFilter"]').prop('checked', false);
        $('input[name="difficultyFilter"]').prop('checked', false);

        // 重置筛选条件
        currentFilters.types = [];
        currentFilters.difficulties = [];

        // 更新筛选按钮文本
        updateFilterText();

        // 关闭筛选面板
        $('#filterPanel').hide();
        $('#filterBtn').removeClass('active');

        // 执行筛选
        loadExercisesWithFilters();
    });

    // 更新筛选按钮文本
    function updateFilterText() {
        var filterParts = [];

        // 添加题型筛选文本
        if (currentFilters.types.length > 0) {
            var typeNames = {
                'single': '单选题',
                'multiple': '多选题',
                'judge': '判断题',
                'fill': '填空题',
                'essay': '主观题'
            };
            var selectedTypeNames = currentFilters.types.map(function(type) {
                return typeNames[type];
            });
            filterParts.push(selectedTypeNames.join('、'));
        }

        // 添加难度筛选文本
        if (currentFilters.difficulties.length > 0) {
            var difficultyNames = {
                'easy': '1级',
                'medium': '2级',
                'hard': '3级'
            };
            var selectedDifficultyNames = currentFilters.difficulties.map(function(difficulty) {
                return difficultyNames[difficulty];
            });
            filterParts.push(selectedDifficultyNames.join('、'));
        }

        // 更新筛选按钮文本
        var filterText = filterParts.length > 0 ? filterParts.join('，') : '全部';
        $('#filterText').text(filterText);
    }
});
</script>
{% endblock %}
