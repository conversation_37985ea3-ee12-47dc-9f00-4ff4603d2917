{% extends "teacher/base.html" %}

{% block title %}智慧课堂系统 - 签到管理{% endblock %}

{% block styles %}
<style>
    .current-class-header {
        background: linear-gradient(135deg, #1e9fff 0%, #0084ff 100%);
        color: white;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .class-info {
        font-size: 16px;
        margin-bottom: 5px;
    }
    .attendance-stats {
        display: flex;
        justify-content: space-around;
        margin-bottom: 20px;
    }
    .stat-item {
        text-align: center;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        flex: 1;
        margin: 0 5px;
    }
    .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #1e9fff;
    }
    .stat-label {
        font-size: 14px;
        color: #666;
        margin-top: 5px;
    }
    .student-item {
        display: flex;
        align-items: center;
        padding: 15px;
        border: 1px solid #e6e6e6;
        border-radius: 8px;
        margin-bottom: 10px;
        background: #fff;
    }
    .student-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: #1e9fff;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 15px;
    }
    .student-info {
        flex: 1;
    }
    .student-name {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .student-id {
        font-size: 12px;
        color: #999;
    }
    .attendance-status {
        text-align: right;
    }
    .status-present {
        color: #52c41a;
    }
    .status-absent {
        color: #ff4d4f;
    }
    .status-late {
        color: #faad14;
    }
    .qr-code-container {
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
{% if current_class %}
<!-- 签到统计 -->
<div class="layui-card">
    <div class="layui-card-header">
        <i class="layui-icon layui-icon-chart"></i> 签到统计
    </div>
    <div class="layui-card-body">
        <div class="attendance-stats">
            <div class="stat-item">
                <div class="stat-number" id="total-students">{{ students|length }}</div>
                <div class="stat-label">总人数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number status-present" id="present-count">
                    {{ students|selectattr('signed_in', 'equalto', 1)|list|length }}
                </div>
                <div class="stat-label">已签到</div>
            </div>
            <div class="stat-item">
                <div class="stat-number status-absent" id="absent-count">
                    {{ students|selectattr('signed_in', 'equalto', 0)|list|length }}
                </div>
                <div class="stat-label">未签到</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="attendance-rate">
                    {% if students|length > 0 %}
                    {{ "%.1f"|format((students|selectattr('signed_in', 'equalto', 1)|list|length / students|length * 100)) }}%
                    {% else %}
                    0%
                    {% endif %}
                </div>
                <div class="stat-label">签到率</div>
            </div>
        </div>
    </div>
</div>



<!-- 学生签到列表 -->
<div class="layui-card">
    <div class="layui-card-header">
        <i class="layui-icon layui-icon-user"></i> 学生签到情况
        <div style="float: right;">
            <button class="layui-btn layui-btn-sm layui-btn-primary" id="export-attendance-btn">
                <i class="layui-icon layui-icon-export"></i> 导出考勤
            </button>
            <button class="layui-btn layui-btn-sm layui-btn-warm" id="manual-checkin-btn">
                <i class="layui-icon layui-icon-add-1"></i> 手动签到
            </button>
        </div>
    </div>
    <div class="layui-card-body">
        {% if students %}
        <div id="students-list">
            {% for student in students %}
            <div class="student-item" data-student-id="{{ student.student_id }}">
                <div class="student-avatar">
                    {{ student.name[0] }}
                </div>
                <div class="student-info">
                    <div class="student-name">{{ student.name }}</div>
                    <div class="student-id">学号：{{ student.student_id }}</div>
                </div>
                <div class="attendance-status">
                    {% if student.signed_in %}
                    <div class="status-present">
                        <i class="layui-icon layui-icon-ok-circle"></i> 已签到
                        {% if student.signin_time %}
                        <br><small>{{ student.signin_time[:19] }}</small>
                        {% endif %}
                    </div>
                    {% else %}
                    <div class="status-absent">
                        <i class="layui-icon layui-icon-close-fill"></i> 未签到
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div style="text-align: center; padding: 40px; color: #999;">
            <i class="layui-icon layui-icon-face-cry" style="font-size: 48px;"></i>
            <p style="font-size: 16px; margin-top: 10px;">该班级暂无学生</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- 手动签到弹窗 -->
<div id="manual-checkin-modal" style="display: none; padding: 20px;">
    <form class="layui-form" id="manual-checkin-form">
        <div class="layui-form-item">
            <label class="layui-form-label">选择学生</label>
            <div class="layui-input-block">
                <select name="student_id" lay-verify="required" lay-search>
                    <option value="">请选择学生</option>
                    {% for student in students %}
                    {% if not student.signed_in %}
                    <option value="{{ student.student_id }}">{{ student.name }} ({{ student.student_id }})</option>
                    {% endif %}
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注</label>
            <div class="layui-input-block">
                <input type="text" name="remark" placeholder="可选，如：迟到、请假等" class="layui-input">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit>
                    <i class="layui-icon layui-icon-ok"></i> 确认签到
                </button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>

{% else %}
<div style="text-align: center; padding: 60px; color: #999;">
    <i class="layui-icon layui-icon-tips" style="font-size: 48px; color: #d9d9d9;"></i>
    <p style="font-size: 18px; margin-top: 15px;">请先在首页选择要管理的课堂</p>
    <button class="layui-btn layui-btn-normal" onclick="location.href='/teacher'">
        <i class="layui-icon layui-icon-return"></i> 返回首页
    </button>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
layui.use(['layer', 'form'], function(){
    var layer = layui.layer;
    var form = layui.form;
    
    {% if current_class %}
    // 手动签到
    $('#manual-checkin-btn').click(function() {
        layer.open({
            type: 1,
            title: '手动签到',
            content: $('#manual-checkin-modal'),
            area: ['400px', '300px'],
            btn: false,
            shadeClose: true
        });
    });

    // 手动签到表单提交
    form.on('submit()', function(data) {
        $.ajax({
            url: '/teacher/manual_checkin/{{ current_class.id }}',
            type: 'POST',
            data: data.field,
            success: function(res) {
                if (res.status === 'success') {
                    layer.msg('签到成功', {icon: 1});
                    layer.closeAll();
                    setTimeout(function() {
                        location.reload();
                    }, 1000);
                } else {
                    layer.msg('签到失败：' + res.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
        return false;
    });

    // 导出考勤
    $('#export-attendance-btn').click(function() {
        window.open('/teacher/export_attendance/{{ current_class.id }}', '_blank');
    });

    // 定时刷新签到状态（每30秒）
    setInterval(function() {
        $.ajax({
            url: '/teacher/get_attendance_status/{{ current_class.id }}',
            type: 'GET',
            success: function(res) {
                if (res.status === 'success') {
                    updateAttendanceDisplay(res.students);
                }
            }
        });
    }, 30000);

    // 更新签到显示
    function updateAttendanceDisplay(students) {
        var presentCount = 0;
        var totalCount = students.length;

        students.forEach(function(student) {
            var studentItem = $('[data-student-id="' + student.student_id + '"]');
            var statusDiv = studentItem.find('.attendance-status');
            
            if (student.signed_in) {
                presentCount++;
                statusDiv.html(`
                    <div class="status-present">
                        <i class="layui-icon layui-icon-ok-circle"></i> 已签到
                        <br><small>${student.signin_time.substring(0, 19)}</small>
                    </div>
                `);
            }
        });

        // 更新统计数据
        $('#present-count').text(presentCount);
        $('#absent-count').text(totalCount - presentCount);
        $('#attendance-rate').text(totalCount > 0 ? (presentCount / totalCount * 100).toFixed(1) + '%' : '0%');
    }
    {% endif %}
});
</script>
{% endblock %}
