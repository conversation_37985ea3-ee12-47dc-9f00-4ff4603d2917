{% extends "teacher/base.html" %}

{% block title %}智慧课堂系统 - 小组管理{% endblock %}

{% block styles %}
<link rel="stylesheet" href="/static/css/teacher.css">
<style>
    /* 页面整体布局 */
    .groups-page {
        background: #f5f5f5;
        min-height: 100vh;
        padding: 20px;
    }

    /* 顶部标题栏 */
    .groups-header {
        background: linear-gradient(135deg, #1e9fff 0%, #0084ff 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        margin-bottom: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 4px 12px rgba(30, 159, 255, 0.3);
    }

    .groups-title {
        display: flex;
        align-items: center;
        font-size: 24px;
        font-weight: 600;
    }

    .groups-title i {
        margin-right: 10px;
        font-size: 28px;
    }

    /* 操作按钮组 */
    .groups-actions {
        display: flex;
        gap: 10px;
    }

    .action-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 10px 20px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
    }

    .action-btn.primary {
        background: #28a745;
        border-color: #28a745;
    }

    .action-btn.primary:hover {
        background: #218838;
    }

    .action-btn.warning {
        background: #ffc107;
        border-color: #ffc107;
        color: #212529;
    }

    .action-btn.warning:hover {
        background: #e0a800;
    }

    /* 未分组学生区域 */
    .ungrouped-section {
        background: #fff;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #ffc107;
    }

    .ungrouped-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        font-size: 18px;
        font-weight: 600;
        color: #333;
    }

    .ungrouped-header i {
        margin-right: 10px;
        color: #ffc107;
    }

    .ungrouped-count {
        background: #ffc107;
        color: white;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 12px;
        margin-left: 10px;
    }

    /* 学生卡片样式 */
    .student-cards {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        min-height: 60px;
        padding: 10px;
        border: 2px dashed #e0e0e0;
        border-radius: 8px;
        background: #fafafa;
    }

    .student-card {
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 10px 15px;
        cursor: move;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .student-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        border-color: #1e9fff;
    }

    .student-card.dragging {
        opacity: 0.5;
        transform: rotate(5deg);
    }

    .student-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 14px;
    }

    .student-info {
        flex: 1;
    }

    .student-name {
        font-weight: 600;
        color: #333;
        font-size: 14px;
    }

    .student-id {
        font-size: 12px;
        color: #666;
    }

    /* 分组区域 */
    .groups-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
    }

    /* 分组卡片样式 */
    .group-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .group-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
    }

    .group-card.drag-over {
        border-color: #1e9fff;
        background: #f0f8ff;
    }

    /* 分组头部 */
    .group-header {
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
    }

    .group-header.group-1 { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
    .group-header.group-2 { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .group-header.group-3 { background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); }
    .group-header.group-4 { background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); }
    .group-header.group-5 { background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); }
    .group-header.group-6 { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); }

    .group-title {
        color: white;
        font-size: 18px;
        font-weight: 600;
        margin: 0;
    }

    .group-stats {
        color: rgba(255, 255, 255, 0.9);
        font-size: 12px;
        margin-top: 4px;
    }

    .group-actions {
        display: flex;
        gap: 5px;
    }

    .group-action-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        padding: 6px 8px;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 12px;
    }

    .group-action-btn:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    /* 分组内容区域 */
    .group-body {
        padding: 20px;
        min-height: 120px;
        border: 2px dashed transparent;
        transition: all 0.3s ease;
    }

    .group-body.drag-over {
        border-color: #1e9fff;
        background: #f0f8ff;
    }

    .group-members {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        min-height: 80px;
    }

    .empty-group {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80px;
        color: #999;
        font-size: 14px;
        border: 2px dashed #e0e0e0;
        border-radius: 8px;
        background: #fafafa;
    }

    /* 拖拽提示 */
    .drop-hint {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #1e9fff;
        font-size: 14px;
        font-weight: 600;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
    }

    .group-body.drag-over .drop-hint {
        opacity: 1;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .groups-header {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }

        .groups-actions {
            flex-wrap: wrap;
            justify-content: center;
        }

        .groups-container {
            grid-template-columns: 1fr;
        }

        .student-cards {
            justify-content: center;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="groups-page">
    {% if current_class %}
    <!-- 顶部标题和操作栏 -->
    <div class="groups-header">
        <div class="groups-title">
            <i class="layui-icon layui-icon-group"></i>
            小组管理
        </div>
        <div class="groups-actions">
            <button class="action-btn" id="add-group-btn">
                <i class="layui-icon layui-icon-add-1"></i>
                新增小组
            </button>
            <button class="action-btn warning" id="smart-group-btn">
                <i class="layui-icon layui-icon-refresh"></i>
                智能分组
            </button>
            <button class="action-btn primary" id="save-groups-btn">
                <i class="layui-icon layui-icon-ok"></i>
                保存分组
            </button>
        </div>
    </div>

    <!-- 未分组学生区域 -->
    <div class="ungrouped-section">
        <div class="ungrouped-header">
            <i class="layui-icon layui-icon-user"></i>
            未分组学生
            <span class="ungrouped-count" id="ungrouped-count">0</span>
        </div>
        <div class="student-cards" id="ungrouped-students" data-group-id="ungrouped">
            <!-- 未分组学生将在这里动态加载 -->
        </div>
    </div>

    <!-- 分组区域 -->
    <div class="groups-container" id="groups-container">
        <!-- 分组卡片将在这里动态加载 -->
    </div>

    {% else %}
    <div style="text-align: center; padding: 60px; color: #999;">
        <i class="layui-icon layui-icon-tips" style="font-size: 48px; color: #d9d9d9;"></i>
        <p style="font-size: 18px; margin-top: 15px;">请先在首页选择要管理的课堂</p>
        <button class="layui-btn layui-btn-normal" onclick="location.href='/teacher'">
            <i class="layui-icon layui-icon-return"></i> 返回首页
        </button>
    </div>
    {% endif %}
</div>

<!-- 智能分组弹窗 -->
<div id="smart-group-modal" style="display: none; padding: 20px;">
    <form class="layui-form" id="smart-group-form">
        <div class="layui-form-item">
            <label class="layui-form-label">分组方式</label>
            <div class="layui-input-block">
                <div style="margin-bottom: 15px;">
                    <div style="border: 1px solid #e6e6e6; border-radius: 8px; padding: 15px; margin-bottom: 10px; cursor: pointer; transition: all 0.3s ease;" class="grouping-option" data-method="by_count">
                        <div style="font-size: 16px; font-weight: bold; margin-bottom: 5px;">按组数分组</div>
                        <div style="font-size: 12px; color: #666;">指定要分成几个小组</div>
                    </div>
                    <div style="border: 1px solid #e6e6e6; border-radius: 8px; padding: 15px; cursor: pointer; transition: all 0.3s ease;" class="grouping-option" data-method="by_size">
                        <div style="font-size: 16px; font-weight: bold; margin-bottom: 5px;">按人数分组</div>
                        <div style="font-size: 12px; color: #666;">指定每组的人数</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layui-form-item" id="count-input" style="display: none;">
            <label class="layui-form-label">组数</label>
            <div class="layui-input-block">
                <input type="number" name="group_count" min="2" max="10" value="4" class="layui-input" placeholder="请输入组数">
            </div>
        </div>
        <div class="layui-form-item" id="size-input" style="display: none;">
            <label class="layui-form-label">每组人数</label>
            <div class="layui-input-block">
                <input type="number" name="group_size" min="2" max="8" value="4" class="layui-input" placeholder="请输入每组人数">
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">分组策略</label>
            <div class="layui-input-block">
                <input type="radio" name="strategy" value="random" title="随机分组" checked>
                <input type="radio" name="strategy" value="balanced" title="均衡分组（按成绩）">
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-input-block">
                <button type="submit" class="layui-btn" lay-submit>
                    <i class="layui-icon layui-icon-ok"></i> 开始分组
                </button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">取消</button>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block scripts %}
<script>
layui.use(['layer', 'form'], function(){
    var layer = layui.layer;
    var form = layui.form;

    {% if current_class %}
    // 全局变量
    var currentGroups = [];
    var allStudents = [];
    var draggedStudent = null;

    // 页面加载时初始化
    $(document).ready(function() {
        loadStudentsAndGroups();
    });

    // 加载学生和分组数据
    function loadStudentsAndGroups() {
        // 加载学生数据
        $.ajax({
            url: '/teacher/get_students/{{ current_class.id }}',
            type: 'GET',
            success: function(res) {
                if (res.status === 'success') {
                    allStudents = res.students;
                    loadGroups();
                } else {
                    layer.msg('加载学生数据失败: ' + res.message);
                }
            },
            error: function() {
                layer.msg('加载学生数据失败，请重试');
            }
        });
    }

    // 加载分组数据
    function loadGroups() {
        $.ajax({
            url: '/teacher/groups/{{ current_class.id }}',
            type: 'GET',
            success: function(res) {
                if (res.status === 'success') {
                    currentGroups = res.groups || [];
                    renderGroupsAndStudents();
                } else {
                    layer.msg('加载分组数据失败: ' + res.message);
                }
            },
            error: function() {
                layer.msg('加载分组数据失败，请重试');
            }
        });
    }

    // 渲染分组和学生
    function renderGroupsAndStudents() {
        renderUngroupedStudents();
        renderGroups();
    }

    // 渲染未分组学生
    function renderUngroupedStudents() {
        var ungroupedStudents = getUngroupedStudents();
        var container = $('#ungrouped-students');
        var countElement = $('#ungrouped-count');

        countElement.text(ungroupedStudents.length);

        var html = '';
        ungroupedStudents.forEach(function(student) {
            html += createStudentCardHtml(student);
        });

        if (ungroupedStudents.length === 0) {
            html = '<div style="text-align: center; color: #999; padding: 20px;">所有学生已分组</div>';
        }

        container.html(html);
    }

    // 获取未分组学生
    function getUngroupedStudents() {
        var groupedStudentIds = [];
        currentGroups.forEach(function(group) {
            if (group.members) {
                group.members.forEach(function(member) {
                    groupedStudentIds.push(member.student_id);
                });
            }
        });

        return allStudents.filter(function(student) {
            return groupedStudentIds.indexOf(student.student_id) === -1;
        });
    }

    // 创建学生卡片HTML
    function createStudentCardHtml(student) {
        var avatar = student.name.charAt(0).toUpperCase();
        return `
            <div class="student-card" draggable="true" data-student-id="${student.student_id}">
                <div class="student-avatar">${avatar}</div>
                <div class="student-info">
                    <div class="student-name">${student.name}</div>
                    <div class="student-id">${student.student_id}</div>
                </div>
            </div>
        `;
    }

    // 渲染分组
    function renderGroups() {
        var container = $('#groups-container');
        var html = '';

        currentGroups.forEach(function(group, index) {
            var groupClass = 'group-' + ((index % 6) + 1);
            var membersHtml = '';

            if (group.members && group.members.length > 0) {
                group.members.forEach(function(member) {
                    membersHtml += createStudentCardHtml(member);
                });
            } else {
                membersHtml = '<div class="empty-group">拖拽学生到此处加入分组</div>';
            }

            html += `
                <div class="group-card" data-group-id="${group.id}">
                    <div class="group-header ${groupClass}">
                        <div>
                            <div class="group-title">${group.group_name}</div>
                            <div class="group-stats">成员数：${group.members ? group.members.length : 0}人</div>
                        </div>
                        <div class="group-actions">
                            <button class="group-action-btn delete-group-btn" data-group-id="${group.id}">
                                <i class="layui-icon layui-icon-delete"></i>
                            </button>
                        </div>
                    </div>
                    <div class="group-body" data-group-id="${group.id}">
                        <div class="group-members">
                            ${membersHtml}
                        </div>
                        <div class="drop-hint">拖拽学生到此处</div>
                    </div>
                </div>
            `;
        });

        if (currentGroups.length === 0) {
            html = `
                <div style="text-align: center; padding: 40px; color: #999; grid-column: 1 / -1;">
                    <i class="layui-icon layui-icon-group" style="font-size: 48px; color: #d9d9d9;"></i>
                    <p style="font-size: 16px; margin-top: 10px;">暂无分组</p>
                    <p style="font-size: 14px; color: #ccc;">点击新增小组或智能分组开始</p>
                </div>
            `;
        }

        container.html(html);

        // 重新绑定拖拽事件
        bindDragEvents();
    }

    // 绑定拖拽事件
    function bindDragEvents() {
        // 拖拽开始
        $(document).off('dragstart', '.student-card').on('dragstart', '.student-card', function(e) {
            draggedStudent = $(this).data('student-id');
            $(this).addClass('dragging');
            e.originalEvent.dataTransfer.effectAllowed = 'move';
        });

        // 拖拽结束
        $(document).off('dragend', '.student-card').on('dragend', '.student-card', function(e) {
            $(this).removeClass('dragging');
            draggedStudent = null;
        });

        // 拖拽进入
        $(document).off('dragover', '.group-body, .student-cards').on('dragover', '.group-body, .student-cards', function(e) {
            e.preventDefault();
            $(this).addClass('drag-over');
        });

        // 拖拽离开
        $(document).off('dragleave', '.group-body, .student-cards').on('dragleave', '.group-body, .student-cards', function(e) {
            $(this).removeClass('drag-over');
        });

        // 放置
        $(document).off('drop', '.group-body, .student-cards').on('drop', '.group-body, .student-cards', function(e) {
            e.preventDefault();
            $(this).removeClass('drag-over');

            if (!draggedStudent) return;

            var targetGroupId = $(this).data('group-id');

            if (targetGroupId === 'ungrouped') {
                // 移动到未分组区域
                removeStudentFromGroup(draggedStudent);
            } else {
                // 移动到指定分组
                addStudentToGroup(draggedStudent, targetGroupId);
            }
        });

        // 双击移除学生
        $(document).off('dblclick', '.group-members .student-card').on('dblclick', '.group-members .student-card', function() {
            var studentId = $(this).data('student-id');
            layer.confirm('确定要将该学生移出分组吗？', function(index) {
                removeStudentFromGroup(studentId);
                layer.close(index);
            });
        });
    }

    // 添加学生到分组
    function addStudentToGroup(studentId, groupId) {
        $.ajax({
            url: '/teacher/add_to_group',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                student_id: studentId,
                group_id: groupId
            }),
            success: function(res) {
                if (res.status === 'success') {
                    if (res.message !== '学生已在该分组中') {
                        layer.msg('添加成功', {icon: 1, time: 1000});
                    }
                    loadGroups();
                } else {
                    layer.msg('添加失败: ' + res.message, {icon: 2});
                }
            },
            error: function(xhr) {
                try {
                    var errorRes = JSON.parse(xhr.responseText);
                    layer.msg('操作失败: ' + errorRes.message, {icon: 2});
                } catch (e) {
                    layer.msg('请求失败，请重试', {icon: 2});
                }
            }
        });
    }

    // 从分组中移除学生
    function removeStudentFromGroup(studentId) {
        $.ajax({
            url: '/teacher/remove_from_group',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                student_id: studentId
            }),
            success: function(res) {
                if (res.status === 'success') {
                    layer.msg('移除成功', {icon: 1, time: 1000});
                    loadGroups();
                } else {
                    layer.msg('移除失败: ' + res.message, {icon: 2});
                }
            },
            error: function(xhr) {
                layer.msg('请求失败，请重试', {icon: 2});
            }
        });
    }

    // 新增小组
    $('#add-group-btn').click(function() {
        layer.prompt({
            title: '新增小组',
            formType: 0,
            value: '第' + (currentGroups.length + 1) + '组'
        }, function(value, index) {
            if (value) {
                $.ajax({
                    url: '/teacher/create_group',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        course_schedule_id: '{{ current_class.id }}',
                        group_name: value,
                        group_description: ''
                    }),
                    success: function(res) {
                        if (res.status === 'success') {
                            layer.msg('创建成功', {icon: 1});
                            loadGroups();
                        } else {
                            layer.msg('创建失败: ' + res.message, {icon: 2});
                        }
                    },
                    error: function(xhr) {
                        layer.msg('请求失败，请重试', {icon: 2});
                    }
                });
            }
            layer.close(index);
        });
    });

    // 智能分组
    $('#smart-group-btn').click(function() {
        layer.open({
            type: 1,
            title: '智能分组',
            content: $('#smart-group-modal'),
            area: ['500px', '600px'],
            btn: false,
            shadeClose: true,
            success: function() {
                // 重新渲染表单
                form.render();

                // 绑定分组方式选择事件
                $('.grouping-option').click(function() {
                    $('.grouping-option').css({
                        'border-color': '#e6e6e6',
                        'background': '#fff'
                    });
                    $(this).css({
                        'border-color': '#1e9fff',
                        'background': '#f0f8ff'
                    });

                    var method = $(this).data('method');
                    if (method === 'by_count') {
                        $('#count-input').show();
                        $('#size-input').hide();
                    } else {
                        $('#count-input').hide();
                        $('#size-input').show();
                    }
                });

                // 默认选择第一个选项
                $('.grouping-option').first().click();
            }
        });
    });

    // 智能分组表单提交
    form.on('submit()', function(data) {
        var selectedMethod = $('.grouping-option').filter(function() {
            return $(this).css('border-color') === 'rgb(30, 159, 255)';
        }).data('method');

        if (!selectedMethod) {
            layer.msg('请选择分组方式', {icon: 2});
            return false;
        }

        var formData = data.field;
        formData.method = selectedMethod;

        $.ajax({
            url: '/teacher/auto_group/{{ current_class.id }}',
            type: 'POST',
            data: formData,
            success: function(res) {
                if (res.status === 'success') {
                    layer.msg('智能分组成功', {icon: 1});
                    layer.closeAll();
                    loadGroups();
                } else {
                    layer.msg('分组失败：' + res.message, {icon: 2});
                }
            },
            error: function() {
                layer.msg('网络错误，请重试', {icon: 2});
            }
        });
        return false;
    });

    // 保存分组
    $('#save-groups-btn').click(function() {
        layer.msg('分组已自动保存', {icon: 1});
    });

    // 删除分组
    $(document).on('click', '.delete-group-btn', function() {
        var groupId = $(this).data('group-id');

        layer.confirm('确定要删除这个分组吗？分组中的学生将变为未分组状态。', function(index) {
            $.ajax({
                url: '/teacher/delete_group/' + groupId,
                type: 'DELETE',
                success: function(res) {
                    if (res.status === 'success') {
                        layer.msg('删除成功', {icon: 1});
                        loadGroups();
                    } else {
                        layer.msg('删除失败: ' + res.message, {icon: 2});
                    }
                },
                error: function(xhr) {
                    layer.msg('请求失败，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    });

    {% endif %}
});
</script>
{% endblock %}
