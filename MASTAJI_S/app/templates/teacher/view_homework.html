{% extends "teacher/base.html" %}

{% block title %}智慧课堂系统 - 查看作业{% endblock %}

{% block styles %}
<style>
    .homework-header {
        background: linear-gradient(135deg, #1e9fff 0%, #0084ff 100%);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    .homework-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
    }
    .homework-meta {
        font-size: 14px;
        opacity: 0.9;
    }
    .homework-stats {
        display: flex;
        gap: 30px;
        margin-top: 15px;
    }
    .stat-item {
        text-align: center;
    }
    .stat-number {
        font-size: 20px;
        font-weight: bold;
        display: block;
    }
    .stat-label {
        font-size: 12px;
        opacity: 0.8;
    }
    .question-item {
        border: 1px solid #e6e6e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 15px;
        background: #fff;
    }
    .question-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }
    .question-number {
        background: #1e9fff;
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: bold;
    }
    .question-type {
        background: #f0f0f0;
        color: #666;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 11px;
    }
    .question-score {
        color: #ff6b6b;
        font-weight: bold;
    }
    .question-content {
        font-size: 16px;
        line-height: 1.6;
        margin-bottom: 15px;
    }
    .question-options {
        margin-bottom: 15px;
    }
    .option-item {
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    .option-item:last-child {
        border-bottom: none;
    }
    .correct-answer {
        background: #f6ffed;
        border: 1px solid #b7eb8f;
        border-radius: 4px;
        padding: 10px;
        color: #52c41a;
    }
    .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: bold;
        margin-left: 10px;
    }
    .status-draft {
        background: #f0f0f0;
        color: #666;
    }
    .status-published {
        background: #e6f7ff;
        color: #1890ff;
    }
    .status-closed {
        background: #fff2e8;
        color: #fa8c16;
    }
</style>
{% endblock %}

{% block content %}
<!-- 作业基本信息 -->
<div class="homework-header">
    <div class="homework-title">
        {{ homework.title }}
        <span class="status-badge status-{{ homework.status }}">
            {% if homework.status == 'draft' %}草稿
            {% elif homework.status == 'published' %}已发布
            {% elif homework.status == 'closed' %}已截止
            {% endif %}
        </span>
    </div>
    <div class="homework-meta">
        {% if homework.course_name %}
        课程：{{ homework.course_name }} ({{ homework.course_code }}) - {{ homework.class_name }}
        {% endif %}
        {% if homework.deadline %}
        | 截止时间：{{ homework.deadline[:19] }}
        {% endif %}
    </div>
    {% if homework.description %}
    <div class="homework-meta" style="margin-top: 10px;">
        作业说明：{{ homework.description }}
    </div>
    {% endif %}
    
    <div class="homework-stats">
        <div class="stat-item">
            <span class="stat-number">{{ homework.question_count or 0 }}</span>
            <span class="stat-label">题目数量</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{{ homework.submitted_count or 0 }}</span>
            <span class="stat-label">已提交</span>
        </div>
        <div class="stat-item">
            <span class="stat-number">{{ homework.total_students or 0 }}</span>
            <span class="stat-label">总人数</span>
        </div>
        {% if homework.submitted_count and homework.total_students %}
        <div class="stat-item">
            <span class="stat-number">{{ "%.1f"|format(homework.submitted_count / homework.total_students * 100) }}%</span>
            <span class="stat-label">完成率</span>
        </div>
        {% endif %}
    </div>
</div>

<!-- 操作按钮 -->
<div class="layui-card">
    <div class="layui-card-header">
        <i class="layui-icon layui-icon-edit"></i> 作业操作
        <div style="float: right;">
            <button class="layui-btn layui-btn-primary" onclick="history.back()">
                <i class="layui-icon layui-icon-return"></i> 返回
            </button>
            <button class="layui-btn layui-btn-normal" onclick="window.open('/teacher/homework_analysis/{{ homework.id }}', '_blank')">
                <i class="layui-icon layui-icon-chart"></i> 查看分析
            </button>
            {% if homework.status == 'draft' %}
            <button class="layui-btn layui-btn-warm" onclick="publisHomework('{{ homework.id }}')">
                <i class="layui-icon layui-icon-release"></i> 发布作业
            </button>
            {% endif %}
        </div>
    </div>
</div>

<!-- 题目列表 -->
<div class="layui-card">
    <div class="layui-card-header">
        <i class="layui-icon layui-icon-list"></i> 题目详情 (共{{ homework.questions|length }}题)
    </div>
    <div class="layui-card-body">
        {% if homework.questions %}
        {% for question in homework.questions %}
        <div class="question-item">
            <div class="question-header">
                <div>
                    <span class="question-number">第{{ loop.index }}题</span>
                    <span class="question-type">
                        {% if question.type == 'single' %}单选题
                        {% elif question.type == 'multiple' %}多选题
                        {% elif question.type == 'judge' %}判断题
                        {% elif question.type == 'fill' %}填空题
                        {% elif question.type == 'text' %}简答题
                        {% else %}{{ question.type }}
                        {% endif %}
                    </span>
                </div>
                <div class="question-score">{{ question.score or 0 }}分</div>
            </div>
            
            <div class="question-content">
                {{ question.desc or question.question or question.text }}
            </div>
            
            {% if question.options %}
            <div class="question-options">
                <strong>选项：</strong>
                {% for option in question.options %}
                <div class="option-item">
                    {% set option_labels = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'] %}
                    {{ option.label or option_labels[loop.index0] or loop.index }}. {{ option.text or option }}
                </div>
                {% endfor %}
            </div>
            {% endif %}
            
            {% if question.answer %}
            <div class="correct-answer">
                <strong>正确答案：</strong>{{ question.answer }}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        {% else %}
        <div style="text-align: center; padding: 40px; color: #999;">
            <i class="layui-icon layui-icon-face-cry" style="font-size: 48px;"></i>
            <p style="font-size: 16px; margin-top: 10px;">该作业暂无题目</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    // 发布作业
    window.publishHomework = function(homeworkId) {
        layer.confirm('确定要发布这个作业吗？发布后学生即可开始作答。', function(index) {
            $.ajax({
                url: '/teacher/publish_homework/' + homeworkId,
                type: 'POST',
                success: function(res) {
                    if (res.status === 'success') {
                        layer.msg('作业发布成功', {icon: 1});
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        layer.msg('发布失败：' + res.message, {icon: 2});
                    }
                },
                error: function() {
                    layer.msg('网络错误，请重试', {icon: 2});
                }
            });
            layer.close(index);
        });
    };
});
</script>
{% endblock %}
