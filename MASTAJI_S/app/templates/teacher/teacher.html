{% extends "teacher/base.html" %}

{% block title %}智慧课堂系统 - 首页{% endblock %}

{% block styles %}
<style>
    /* 主卡片样式 */
    .layui-card {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    .layui-card-header {
        font-weight: bold;
        font-size: 16px;
    }

    /* 桌面共享区域 */
    #videoContainer {
        position: relative;
        border-radius: 8px;
        overflow: hidden;
        background: #f0f2f5;
        min-height: 400px; /* 保持一个最小高度 */
    }

    #videoPlaceholder {
        height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f0f2f5;
    }

    .placeholder-content {
        text-align: center;
        color: #999;
    }

    /* 流信息 */
    .stream-info-item {
        position: relative;
    }
    .stream-info-item .layui-input {
        padding-right: 50px;
    }
    .copy-btn {
        position: absolute;
        right: 5px;
        top: 5px;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
        color: #666;
    }
    .copy-btn:hover {
        color: #1E9FFF;
    }

    /* 聊天区域样式 */
    .chat-panel {
        height: 480px; /* 根据需要调整高度 */
        display: flex;
        flex-direction: column;
    }
    .chat-messages {
        flex-grow: 1;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        background-color: #f9f9f9;
        margin-bottom: 10px;
    }
    .chat-message {
        margin-bottom: 10px;
        line-height: 1.5;
    }
    .chat-message .user {
        font-weight: bold;
        color: #1E9FFF;
        margin-right: 8px;
    }
    .chat-message .user.student {
        color: #5FB878;
    }
    .chat-message .user.system {
        color: #FF5722;
    }
    .chat-message .message {
        color: #333;
    }
    .chat-input {
        display: flex;
    }
    .chat-input input {
        flex-grow: 1;
        margin-right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="layui-row layui-col-space20">
    <!-- 桌面共享区域 -->
    <div class="layui-col-md8">
        <div class="layui-card">
            <div class="layui-card-header">
                <i class="layui-icon layui-icon-screen"></i> 桌面共享
                <div style="float: right;">
                    <button id="startCaptureBtn" class="layui-btn layui-btn-normal layui-btn-sm">
                        <i class="layui-icon layui-icon-play"></i> 开始共享
                    </button>
                    <button id="stopCaptureBtn" class="layui-btn layui-btn-danger layui-btn-sm" style="display: none;">
                        <i class="layui-icon layui-icon-pause"></i> 停止共享
                    </button>
                </div>
            </div>
            <div class="layui-card-body">
                <div id="videoContainer">
                    <video id="desktopVideo" controls style="width: 100%; height: 400px; background: #000; display: none;">
                        您的浏览器不支持视频播放
                    </video>
                    <div id="videoPlaceholder" class="video-placeholder">
                        <div class="placeholder-content">
                            <i class="layui-icon layui-icon-video" style="font-size: 64px; color: #d9d9d9;"></i>
                            <p style="margin-top: 10px;">正在准备桌面共享...</p>
                        </div>
                    </div>
                    <!-- 弹幕区域 -->
                    <div id="danmakuArea" style="position: absolute; top: 0; left: 0; width: 100%; height: 90%; /* 避免遮挡原生控制条 */ overflow: hidden; pointer-events: none; z-index: 10;">
                        <!-- 弹幕将在此处动态添加 -->
                    </div>
                </div>
                <div id="streamInfo" style="margin-top: 15px; display: none;">
                    <div class="layui-row layui-col-space10">
                        <div class="layui-col-md6">
                            <label class="layui-form-label" style="text-align: left; padding-left: 0;">HLS 流地址 (推荐)</label>
                            <div class="stream-info-item">
                                <input type="text" id="hlsUrl" class="layui-input" readonly>
                                <i class="layui-icon layui-icon-file-b copy-btn" data-clipboard-target="#hlsUrl" title="复制"></i>
                            </div>
                        </div>
                        <div class="layui-col-md6">
                            <label class="layui-form-label" style="text-align: left; padding-left: 0;">RTSP 流地址</label>
                            <div class="stream-info-item">
                                <input type="text" id="rtspUrl" class="layui-input" readonly>
                                <i class="layui-icon layui-icon-file-b copy-btn" data-clipboard-target="#rtspUrl" title="复制"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 互动区域 -->
    <div class="layui-col-md4">
        <div class="layui-card">
            <div class="layui-card-header">
                <i class="layui-icon layui-icon-dialogue"></i> 课堂互动
            </div>
            <div class="layui-card-body chat-panel">
                <div class="chat-messages" id="chatMessages">
                    <!-- 消息将显示在这里 -->
                </div>
                <div class="chat-input">
                    <input type="text" id="chatInput" class="layui-input" placeholder="输入消息...">
                    <button id="sendChatBtn" class="layui-btn">发送</button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.7.2/dist/socket.io.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/hls.js@1.4.10/dist/hls.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/clipboard@2.0.8/dist/clipboard.min.js"></script>
<script>
$(document).ready(function() {
    layui.use(['element', 'layer'], function(){
        var layer = layui.layer;
        var isCapturing = false;
        var statusCheckInterval = null;
        var hlsPlayer = null;
        var socket = null;
        var danmakuContainerElement = null;

        // 初始化
        checkStatus();
        startStatusMonitoring();
        bindButtonEvents();
        initializeChat();
        
        // 自动开始共享
        setTimeout(function() {
            if (!$('#stopCaptureBtn').is(':visible')) {
                $('#startCaptureBtn').trigger('click');
            }
        }, 1000);

        // 获取弹幕容器元素
        danmakuContainerElement = document.getElementById('danmakuArea');

        new ClipboardJS('.copy-btn').on('success', function(e) {
            layer.msg('复制成功!', {icon: 1, time: 1000});
            e.clearSelection();
        });

        // 绑定按钮事件
        function bindButtonEvents() {
            $('#startCaptureBtn').on('click', handleStartCapture);
            $('#stopCaptureBtn').on('click', handleStopCapture);
            $('#sendChatBtn').on('click', sendChatMessage);
            $('#chatInput').on('keypress', function(e) {
                if (e.which == 13) { // Enter 键
                    sendChatMessage();
                }
            });
        }

        // 处理开始捕获
        function handleStartCapture() {
            var btn = $(this);
            btn.prop('disabled', true).addClass('layui-btn-disabled');
            layer.load(1, {shade: [0.1,'#fff']});

            $.ajax({
                url: '/teacher/api/desktop_capture/start',
                type: 'POST',
                success: function(res) {
                    if (res.status === 'success') {
                        layer.msg('桌面捕获已开始', {icon: 1});
                        updateCaptureUI(true);
                        setTimeout(startVideoPlayback, 3000); // 等待流准备
                    } else {
                        layer.msg(res.message || '启动失败', {icon: 2});
                    }
                },
                error: function(xhr, status, error) {
                    layer.msg('请求失败: ' + error, {icon: 2});
                },
                complete: function() {
                    layer.closeAll('loading');
                    btn.prop('disabled', false).removeClass('layui-btn-disabled');
                }
            });
        }

        // 处理停止捕获
        function handleStopCapture() {
            var btn = $(this);
            btn.prop('disabled', true).addClass('layui-btn-disabled');
            layer.load(1, {shade: [0.1,'#fff']});
            stopDesktopCapture(false, btn);
        }

        // 停止桌面捕获（支持强制）
        function stopDesktopCapture(force, btn) {
            var url = force ? '/teacher/api/desktop_capture/force_stop' : '/teacher/api/desktop_capture/stop';
            var actionText = force ? '强制停止' : '停止';

            $.ajax({
                url: url,
                type: 'POST',
                timeout: 10000,
                success: function(res) {
                    layer.closeAll('loading');
                    if (res.status === 'success') {
                        layer.msg('桌面捕获已' + actionText, {icon: 1});
                        updateCaptureUI(false);
                        stopVideoPlayback();
                        setTimeout(checkStatus, 1000);
                    } else {
                        if (!force) {
                            layer.confirm('停止失败，是否强制停止？', {
                                title: '操作确认',
                                icon: 3
                            }, function(index) {
                                layer.close(index);
                                stopDesktopCapture(true, btn);
                            }, function() {
                                btn.prop('disabled', false).removeClass('layui-btn-disabled');
                            });
                        } else {
                            layer.msg(actionText + '失败: ' + res.message, {icon: 2});
                            btn.prop('disabled', false).removeClass('layui-btn-disabled');
                        }
                    }
                },
                error: function(xhr, status, error) {
                    layer.closeAll('loading');
                    if (status === 'timeout' && !force) {
                        layer.confirm('停止请求超时，是否强制停止？', {
                           title: '操作确认',
                           icon: 3
                        }, function(index) {
                            layer.close(index);
                            stopDesktopCapture(true, btn);
                        }, function() {
                           btn.prop('disabled', false).removeClass('layui-btn-disabled');
                        });
                    } else {
                        layer.msg(actionText + '请求出错: ' + error, {icon: 2});
                        btn.prop('disabled', false).removeClass('layui-btn-disabled');
                    }
                }
            });
        }

        // 检查状态
        function checkStatus() {
            $.ajax({
                url: '/teacher/api/desktop_capture/status',
                type: 'GET',
                success: function(res) {
                    if (res.status === 'success') {
                        updateStatusUI(res.data);
                    }
                },
                error: function() {
                    // 静默失败，避免过多提示
                    console.error('状态检查请求失败');
                }
            });
        }

        // 更新状态UI
        function updateStatusUI(data) {
            var actualCapturing = data.capturing && data.ffmpeg_process_running;

            // UI
            updateCaptureUI(actualCapturing);
            if (isCapturing !== actualCapturing) {
                isCapturing = actualCapturing;
                if(!actualCapturing) stopVideoPlayback();
            }

            // Stream URL
            if (data.stream_url) {
                $('#hlsUrl').val(data.stream_url.hls);
                $('#rtspUrl').val(data.stream_url.rtsp);
            }
        }

        // 更新捕获控制UI
        function updateCaptureUI(capturing) {
            if (capturing) {
                $('#startCaptureBtn').hide();
                $('#stopCaptureBtn').show().prop('disabled', false).removeClass('layui-btn-disabled');
                $('#streamInfo').slideDown();
            } else {
                $('#startCaptureBtn').show();
                $('#stopCaptureBtn').hide();
                $('#streamInfo').slideUp();
                // 确保按钮可用
                $('#startCaptureBtn').prop('disabled', false).removeClass('layui-btn-disabled');
            }
        }

        // 开始视频播放
        function startVideoPlayback() {
            var video = document.getElementById('desktopVideo');
            var hlsUrl = $('#hlsUrl').val();

            if (!hlsUrl) return;

            if (Hls.isSupported()) {
                if (hlsPlayer) hlsPlayer.destroy();
                hlsPlayer = new Hls({
                    lowLatencyMode: true,
                    backBufferLength: 90
                });
                hlsPlayer.loadSource(hlsUrl);
                hlsPlayer.attachMedia(video);
                hlsPlayer.on(Hls.Events.MANIFEST_PARSED, function() {
                    video.play().catch(e => console.error("自动播放失败", e));
                    $('#videoPlaceholder').hide();
                    $('#desktopVideo').show();
                });
                hlsPlayer.on(Hls.Events.ERROR, function(event, data) {
                    if (data.fatal) {
                        console.error('HLS 播放错误:', data);
                    }
                });
            } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                video.src = hlsUrl;
                video.addEventListener('loadedmetadata', function() {
                    video.play();
                    $('#videoPlaceholder').hide();
                    $('#desktopVideo').show();
                });
            } else {
                layer.msg('您的浏览器不支持HLS播放', {icon: 0});
            }
        }

        // 停止视频播放
        function stopVideoPlayback() {
            var video = document.getElementById('desktopVideo');
            if (hlsPlayer) {
                hlsPlayer.destroy();
                hlsPlayer = null;
            }
            video.pause();
            video.src = '';
            $('#desktopVideo').hide();
            $('#videoPlaceholder').show();
        }

        // 开始状态监控
        function startStatusMonitoring() {
            if (statusCheckInterval) clearInterval(statusCheckInterval);
            statusCheckInterval = setInterval(checkStatus, 5000);
        }

        // 初始化聊天功能
        function initializeChat() {
            // 连接到 Socket.IO 服务器
            // 注意：这里的URL需要根据实际情况调整，如果Flask和SocketIO在同一个服务器上，可以留空
            socket = io({
                transports: ['websocket']
            });

            // 定义课堂房间
            const room = 'classroom_main';

            socket.on('connect', function() {
                console.log('已连接到聊天服务器');
                // 加入房间
                socket.emit('join', {room: room});
            });

            socket.on('disconnect', function() {
                console.log('与聊天服务器断开连接');
            });

            socket.on('status', function(data) {
                console.log('服务器状态: ' + data.msg);
            });

            socket.on('chat_message', function(data) {
                appendChatMessage(data.user, data.message);
            });
        }
        
        // 显示视频弹幕
        function showDanmakuOnVideo(messageText, user) {
            if (!danmakuContainerElement) {
                console.error("Danmaku container #danmakuArea not found!");
                return;
            }
            if (!messageText || messageText.trim() === "") {
                return;
            }

            const danmaku = document.createElement('div');
            // 弹幕是否显示用户名
            danmaku.textContent = `${user}: ${messageText}`; 
            // danmaku.textContent = messageText; // 只显示消息内容

            danmaku.style.position = 'absolute';
            danmaku.style.right = '0px'; // 从右边出现
            danmaku.style.whiteSpace = 'nowrap'; // 防止文字换行
            danmaku.style.color = '#fff'; // 白色字体
            danmaku.style.fontSize = Math.floor(Math.random() * 10 + 18) + 'px'; // 随机字号 (18-27px)
            danmaku.style.fontWeight = 'bold';
            danmaku.style.textShadow = '1px 1px 2px rgba(0,0,0,0.7)'; // 文字阴影，增强可读性
            
            const containerHeight = danmakuContainerElement.clientHeight;
            const danmakuHeightEstimate = parseInt(danmaku.style.fontSize, 10) * 1.2; // 估算弹幕高度
            let topPosition = Math.floor(Math.random() * (containerHeight - danmakuHeightEstimate));
            if (topPosition < 0) topPosition = 0; // 确保不为负
            danmaku.style.top = topPosition + 'px';

            danmakuContainerElement.appendChild(danmaku);

            danmaku.getBoundingClientRect(); // 强制浏览器重绘以确保动画初始状态正确应用

            const animationDuration = Math.random() * 5 + 8; // 动画时长 8-13 秒
            danmaku.style.transition = `transform ${animationDuration}s linear`;
            
            const moveDistance = danmakuContainerElement.clientWidth + danmaku.offsetWidth;
            danmaku.style.transform = `translateX(-${moveDistance}px)`; 

            danmaku.addEventListener('transitionend', function() {
                danmaku.remove();
            });
        }
        // 发送聊天消息
        function sendChatMessage() {
            var input = $('#chatInput');
            var message = input.val().trim();
            if (message) {
                const room = 'classroom_main';
                socket.emit('send_message', {room: room, message: message});
                input.val(''); // 清空输入框
            }
        }
        
        // 追加聊天消息到显示框
        function appendChatMessage(user, message) {
            var messagesContainer = $('#chatMessages');
            var userClass = 'user'; // 默认样式
            if (user === '系统') {
                userClass += ' system';
            } else if (user !== '{{ session.teacher_name }}') { // 假设老师的用户名在session中
                 userClass += ' student';
            }

            var messageHtml = `
                <div class="chat-message">
                    <span class="${userClass}">${user}:</span>
                    <span class="message">${message}</span>
                </div>
            `;
            messagesContainer.append(messageHtml);
            // 滚动到底部
            messagesContainer.scrollTop(messagesContainer[0].scrollHeight);

            // 同时将消息作为弹幕显示在视频上
            showDanmakuOnVideo(message, user);
        }

        // 页面卸载时清理
        $(window).on('beforeunload', function() {
            if (statusCheckInterval) clearInterval(statusCheckInterval);
            if (hlsPlayer) hlsPlayer.destroy();
            if (socket) {
                socket.emit('leave', {room: 'classroom_main'});
                socket.disconnect();
            }
        });
    });
});
</script>
{% endblock %}
