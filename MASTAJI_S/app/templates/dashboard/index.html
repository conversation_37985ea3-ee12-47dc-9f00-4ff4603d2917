{% extends "dashboard/base.html" %}

{% block title %}课程班级 - 智慧课堂系统{% endblock %}

{%block styles%}
<style>
.course-card {
    background: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.course-card:hover {
    border-color: #009688;
    box-shadow: 0 4px 12px rgba(0,150,136,0.15);
    transform: translateY(-2px);
}

.course-card[data-status="in_progress"] {
    border-color: #FFB800;
    background: linear-gradient(135deg, #fff 0%, #fffbf0 100%);
}

.course-card[data-status="completed"] {
    opacity: 0.7;
    cursor: default;
}

.course-card[data-status="completed"]:hover {
    transform: none;
    box-shadow: none;
}

.course-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.course-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    flex: 1;
    margin-right: 10px;
}

.course-status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    white-space: nowrap;
}

.status-scheduled {
    background: #e8f4fd;
    color: #1E9FFF;
}

.status-in_progress {
    background: #fff7e6;
    color: #FFB800;
}

.status-completed {
    background: #f0f9f0;
    color: #5FB878;
}

.course-info {
    margin-bottom: 15px;
}

.info-item {
    display: flex;
    margin-bottom: 8px;
    font-size: 14px;
}

.info-label {
    color: #666;
    width: 80px;
    flex-shrink: 0;
}

.info-value {
    color: #333;
    flex: 1;
}

.course-actions {
    text-align: center;
    padding-top: 10px;
    border-top: 1px solid #f0f0f0;
}

.course-actions .layui-btn {
    width: 100%;
}
</style>

{% endblock %}

{% block content %}
<div class="content-card">
    <div class="layui-card-header" style="border-bottom: 1px solid #e6e6e6; padding-bottom: 15px; margin-bottom: 20px;">
        <h2 style="margin: 0; color: #333;">课程
        </h2>
    </div>

    {% if error %}
    <div class="layui-alert layui-alert-danger">
        <i class="layui-icon layui-icon-close"></i> 加载课程失败：{{ error }}
    </div>
    {% endif %}

    {% if courses %}
    <div class="layui-row layui-col-space20">
        {% for course in courses %}
        <div class="layui-col-md6 layui-col-lg4">
            <div class="course-card" data-id="{{ course.id }}" data-status="{{ course.status }}">
                <div class="course-header">
                    <div class="course-title">{{ course.course_name }}</div>
                    <div class="course-status status-{{ course.status }}">
                        {% if course.status == 'scheduled' %}
                        <i class="layui-icon layui-icon-time"></i> 待上课
                        {% elif course.status == 'in_progress' %}
                        <i class="layui-icon layui-icon-play"></i> 进行中
                        {% elif course.status == 'completed' %}
                        <i class="layui-icon layui-icon-ok"></i> 已结束
                        {% endif %}
                    </div>
                </div>

                <div class="course-info">
                    <div class="info-item">
                        <span class="info-label">课程代码：</span>
                        <span class="info-value">{{ course.course_code }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">班级：</span>
                        <span class="info-value">{{ course.class_name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">教室：</span>
                        <span class="info-value">{{ course.classroom_name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">时间：</span>
                        <span class="info-value">{{ course.day_of_week }} {{ course.start_time }}-{{ course.end_time }}</span>
                    </div>
                </div>

                <div class="course-actions">
                    {% if course.status == 'scheduled' %}
                    <button class="layui-btn layui-btn-normal layui-btn-sm start-class-btn" data-id="{{ course.id }}">
                        <i class="layui-icon layui-icon-play"></i> 开始上课
                    </button>
                    {% elif course.status == 'in_progress' %}
                    <button class="layui-btn layui-btn-warm layui-btn-sm enter-class-btn" data-id="{{ course.id }}">
                        <i class="layui-icon layui-icon-login"></i> 进入课堂
                    </button>
                    {% else %}
                    <button class="layui-btn layui-btn-disabled layui-btn-sm" disabled>
                        <i class="layui-icon layui-icon-ok"></i> 已结束
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div style="text-align: center; padding: 60px 20px;">
        <i class="layui-icon layui-icon-face-cry" style="font-size: 80px; color: #ccc; margin-bottom: 20px;"></i>
        <h3 style="color: #666; margin-bottom: 10px;">暂无课程安排</h3>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;

    // 开始上课功能
    $(document).on('click', '.start-class-btn', function(e){
        e.stopPropagation();
        var courseId = $(this).data('id');

        layer.confirm('确定要开始上课吗？', {
            title: '开始上课',
            icon: 3
        }, function(index){
            $.ajax({
                url: '/dashboard/start_class/' + courseId,
                type: 'POST',
                success: function(res){
                    if(res.status === 'success'){
                        layer.msg('课程已开始，正在进入上课系统...', {icon: 1});
                        setTimeout(function() {
                            window.location.href = '/dashboard/enter_class/' + courseId;
                        }, 1000);
                    } else {
                        layer.msg('操作失败: ' + res.message, {icon: 2});
                    }
                },
                error: function(xhr){
                    layer.msg('请求失败: ' + xhr.responseText, {icon: 2});
                }
            });

            layer.close(index);
        });
    });

    // 进入课堂功能
    $(document).on('click', '.enter-class-btn', function(e){
        e.stopPropagation();
        var courseId = $(this).data('id');
        window.location.href = '/dashboard/enter_class/' + courseId;
    });

    // 点击卡片进入课堂（仅对进行中的课程有效）
    $(document).on('click', '.course-card', function(){
        var status = $(this).data('status');
        var courseId = $(this).data('id');

        if (status === 'in_progress') {
            window.location.href = '/dashboard/enter_class/' + courseId;
        }
    });
});
</script>
{% endblock %}
