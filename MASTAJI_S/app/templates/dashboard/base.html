<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}智慧课堂系统{% endblock %}</title>
    <link rel="stylesheet" href="/static/css/layui.css">
    {% block styles %}{% endblock %}
    <style>
        body {
            background: #f5f7fa;
            font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
        }

        /* 主要内容区域 */
        .layui-body {
            left: 220px;
            background: #f5f7fa;
            padding: 24px;
        }

        /* 欢迎区域样式 */
        .welcome-section {
            margin-bottom: 24px;
        }

        .welcome-section h1 {
            font-size: 24px;
            color: #1f2329;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .welcome-section .date {
            color: #86909c;
            font-size: 14px;
        }

        /* 卡片容器样式 */
        .card-container {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            padding: 24px;
        }

        /* 操作按钮样式 */
        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 16px;
            margin-top: 24px;
        }

        .action-button {
            background: #fff;
            border: 1px solid #e5e6eb;
            border-radius: 8px;
            padding: 24px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .action-button:hover {
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .action-button .icon {
            width: 48px;
            height: 48px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f0f5ff;
            border-radius: 8px;
            color: #1890ff;
        }

        .action-button .text {
            font-size: 14px;
            color: #1f2329;
        }

        /* 课程卡片样式 */
        .course-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 24px;
        }

        .course-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e5e6eb;
            transition: all 0.3s;
        }

        .course-card:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            transform: translateY(-2px);
        }

        .course-card .title {
            font-size: 16px;
            font-weight: 500;
            color: #1f2329;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .course-card .status {
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            background: #f0f5ff;
            color: #1890ff;
        }

        .course-card .status.ended {
            background: #f5f5f5;
            color: #86909c;
        }

        .course-info {
            color: #4e5969;
            font-size: 14px;
            line-height: 1.8;
        }

        .course-info p {
            margin: 4px 0;
            display: flex;
            align-items: center;
        }

        .course-info .label {
            color: #86909c;
            width: 70px;
        }

        .enter-btn {
            width: 100%;
            margin-top: 16px;
            height: 36px;
            line-height: 36px;
            background: #1890ff;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .enter-btn:hover {
            background: #40a9ff;
        }

        .enter-btn.disabled {
            background: #f5f5f5;
            color: #86909c;
            cursor: not-allowed;
        }

        /* 资源库导航栏样式 */
        .resource-tabs {
            background: #fff;
            margin: -24px -24px 24px;
            padding: 0 24px;
            border-bottom: 1px solid #f0f0f0;
        }

        .resource-tabs .layui-nav {
            background: transparent;
            padding: 0;
            height: 56px;
            line-height: 56px;
        }

        .resource-tabs .layui-nav-item {
            margin: 0 32px 0 0;
            background: transparent !important;
        }

        .resource-tabs .layui-nav-item a {
            color: #4e5969;
            font-size: 15px;
            padding: 0;
            margin: 0;
            position: relative;
            background: transparent !important;
            height: 56px;
            line-height: 56px;
        }

        .resource-tabs .layui-nav-item a:hover {
            background: transparent !important;
            color: #1890ff;
        }

        .resource-tabs .layui-this a {
            color: #1890ff !important;
            background: transparent !important;
        }

        .resource-tabs .layui-nav-item a:after {
            content: ''; 
            position: absolute;
            bottom: 0; 
            left: 0; 
            right: 0; 
            height: 0; 
            background: transparent;
            height: 2px;
            background: transparent;
            transition: all 0.3s;
        }

        .resource-tabs .layui-this a:after {
            background: #1890ff;
        }
    </style>
</head>
<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin">
        <!-- 头部导航 -->
        <div class="layui-header">
            <div class="layui-logo">智慧课堂系统</div>
            <ul class="layui-nav layui-layout-right">
                <li class="layui-nav-item">
                    <a href="javascript:;">
                        {{ session.teacher_name }}
                    </a>
                </li>
                <li class="layui-nav-item">
                    <a href="/teacher/logout" title="退出">
                        <i class="layui-icon layui-icon-logout"></i>
                    </a>
                </li>
            </ul>
        </div>

        <!-- 内容主体区域 -->
        <div class="layui-body">
            <div class="welcome-section">
                <div class="date" id="dashboard-date"></div>
            </div>

            <div class="card-container">
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <script src="/static/js/jquery.min.js"></script>
    <script src="/static/js/layui.js"></script>
    <script>
        layui.use(['element', 'layer'], function(){
            var element = layui.element;
            var layer = layui.layer;
            element.init();
        });

        (function(){
            var date = new Date();
            var weekArr = ['日','一','二','三','四','五','六'];
            var y = date.getFullYear();
            var m = (date.getMonth()+1).toString().padStart(2,'0');
            var d = date.getDate().toString().padStart(2,'0');
            var w = weekArr[date.getDay()];
            document.getElementById('dashboard-date').innerText = `${y}年${m}月${d}日 星期${w}`;
        })();
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
