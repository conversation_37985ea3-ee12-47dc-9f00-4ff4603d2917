<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂系统</title>
    <link rel="stylesheet" href="/static/css/layui.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 20px;
        }
        .welcome-container {
            width: 100%;
            max-width: 800px;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(15px);
            padding: 50px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        .welcome-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        .logo {
            font-size: 48px;
            font-weight: 700;
            color: #333;
            margin-bottom: 30px;
        }
        .subtitle {
            font-size: 18px;
            color: #666;
            margin-bottom: 50px;
        }
        .btn-group {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
        }
        .nav-btn {
            padding: 15px 40px;
            font-size: 18px;
            border-radius: 12px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }
        .admin-btn {
            background: white;
            color: #666;
            border: 2px solid #e8e8e8;
        }
        .admin-btn:hover {
            border-color: #667eea;
            color: #667eea;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="logo">
            <i class="layui-icon layui-icon-platform"></i>
            智慧课堂系统
        </div>
        <div class="subtitle">
            一体化智能教学平台，让教与学变得更简单
        </div>
        <div class="btn-group">
            <a href="/login" class="nav-btn login-btn">
                <i class="layui-icon layui-icon-user"></i>
                用户登录
            </a>
            <a href="/admin" class="nav-btn admin-btn">
                <i class="layui-icon layui-icon-set"></i>
                管理系统
            </a>
        </div>
    </div>
</body>
</html>