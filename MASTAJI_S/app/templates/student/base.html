<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}智慧课堂系统{% endblock %}</title>
        <link rel="stylesheet" href="/static/css/layui.css">
    <style>
        body { background-color: #f2f2f2; }
        .layui-logo { color: #000; }
        .layui-nav .layui-nav-item a {
            color: #333 !important;
        }
        .layui-nav .layui-nav-item a:hover {
            color: #009688 !important;
        }
        .main-wrapper {
            background-color: #fff;
            border-radius: 4px;
            padding: 20px;
            min-height: calc(100vh - 90px);
        }

        /* 右侧悬浮菜单样式 */
        .floating-menu {
            position: fixed;
            right: 20px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 999;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 10px 0;
            width: 50px;
            transition: all 0.3s ease;
        }

        .floating-menu-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 10px 0;
            color: #666;
            cursor: pointer;
            text-decoration: none;
            position: relative;
            transition: all 0.3s ease;
        }

        .floating-menu-item:hover {
            color: #1E9FFF;
            background: #f5f5f5;
        }

        .floating-menu-item.active {
            color: #1E9FFF;
        }

        .floating-menu-item i {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .floating-menu-item span {
            font-size: 12px;
            text-align: center;
            display: none;
            position: absolute;
            right: 60px;
            background: #333;
            color: #fff;
            padding: 5px 10px;
            border-radius: 4px;
            white-space: nowrap;
        }

        .floating-menu-item:hover span {
            display: block;
        }
    </style>
    {% block styles %}{% endblock %}
</head>
<body>
<div class="layui-layout">

    <div class="layui-body" style="left: 0; padding: 15px;">
        <div class="main-wrapper">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- 右侧悬浮菜单 -->
    <div class="floating-menu" id="floatingMenu">
        <a href="{{ url_for('student.live_player') }}" class="floating-menu-item{% if current_page == 'live' %} active{% endif %}">
            <i class="layui-icon layui-icon-play"></i>
            <span>直播</span>
        </a>
        <a href="{{ url_for('student.student_homework') }}" class="floating-menu-item{% if current_page == 'homework' %} active{% endif %}">
            <i class="layui-icon layui-icon-form"></i>
            <span>作业</span>
        </a>
        <a href="javascript:;" id="student-signin-btn" class="floating-menu-item">
            <i class="layui-icon layui-icon-auz"></i>
            <span>签到</span>
        </a>
    </div>

</div>

<script src="/static/js/layui.js"></script>
<script src="/static/js/jquery.min.js"></script>
<script>
    layui.use(['element', 'layer'], function(){
        var element = layui.element;
        var layer = layui.layer;
        var $ = layui.jquery;

        $('#student-signin-btn').on('click', function() {
            // 发送签到请求
            $.post("{{ url_for('student.sign_in_ajax') }}", function(res) {
                if (res.code === 200) {
                    layer.msg(res.message, { icon: 1, time: 1500 });
                } else {
                    layer.msg(res.message, { icon: 2, time: 2000 });
                }
            }).fail(function() {
                layer.msg('请求失败，请检查网络', { icon: 2 });
            });
        });
    });
</script>
{% block scripts %}{% endblock %}
</body>
</html> 