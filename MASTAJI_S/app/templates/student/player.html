{% extends "student/base.html" %}

{% block title %}课程直播 - 智慧课堂系统{% endblock %}

{% block styles %}
<style>
    .video-wrapper {
        position: relative;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        width: 100%;
        padding-top: 56.25%; /* 16:9 aspect ratio */
    }
    #videoContainer {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
    #liveVideo {
        width: 100%;
        height: 100%;
        background-color: #000;
    }
    /* 添加弹幕容器样式 */
    #danmakuArea {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 90%;  /* 避免遮挡视频控制条 */
        pointer-events: none;
        z-index: 10;
        overflow: hidden;
    }
    .placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background: #f0f2f5;
        color: #999;
        text-align: center;
    }
    .placeholder i {
        font-size: 60px;
        margin-bottom: 20px;
    }
    .status-bar {
        padding: 15px;
        background-color: #fff;
        border-radius: 8px;
        margin-top: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        display: flex;
        justify-content: space-between;
        align-items: center;
        border: 1px solid #e6e6e6;
    }
    .status-indicator {
        font-size: 16px;
        font-weight: 500;
    }
    .status-dot {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 8px;
        vertical-align: middle;
    }
    .status-dot.offline {
        background-color: #f5222d;
    }
    .status-dot.online {
        background-color: #52c41a;
    }
    .status-dot.loading {
        background-color: #faad14;
    }

    /* 聊天区域样式 */
    .chat-card {
        margin-top: 20px;
    }
    .chat-panel {
        height: 400px; /* 或者根据需要调整 */
        display: flex;
        flex-direction: column;
    }
    .chat-messages {
        flex-grow: 1;
        overflow-y: auto;
        padding: 10px;
        border: 1px solid #f0f0f0;
        border-radius: 4px;
        background-color: #f9f9f9;
        margin-bottom: 10px;
    }
    .chat-message {
        margin-bottom: 10px;
        line-height: 1.5;
    }
    .chat-message .user {
        font-weight: bold;
        color: #5FB878; /* 学生默认为绿色 */
        margin-right: 8px;
    }
    .chat-message .user.teacher {
        color: #1E9FFF; /* 老师为蓝色 */
    }
    .chat-message .user.system {
        color: #FF5722;
    }
    .chat-message .message {
        color: #333;
    }
    .chat-input {
        display: flex;
    }
    .chat-input input {
        flex-grow: 1;
        margin-right: 10px;
    }
</style>
{% endblock %}

{% block content %}
<div class="layui-fluid" style="padding: 0;">
    <div class="layui-row layui-col-space20">
        <div class="layui-col-md8">
            <div class="video-wrapper">
                <div id="videoContainer">
                    <video id="liveVideo" controls style="display: none;"></video>
                    <div id="videoPlaceholder" class="placeholder">
                        <i class="layui-icon layui-icon-play"></i>
                        <p>等待教师开始直播...</p>
                    </div>
                    <!-- 添加弹幕容器 -->
                    <div id="danmakuArea"></div>
                </div>
            </div>
            <div class="status-bar">
                <div id="liveStatus" class="status-indicator">
                    <span class="status-dot offline"></span> 离线
                </div>
                <div>
                    <button id="reconnectBtn" class="layui-btn layui-btn-sm layui-btn-primary">
                        <i class="layui-icon layui-icon-refresh"></i> 手动刷新
                    </button>
                </div>
            </div>
        </div>
        <div class="layui-col-md4">
            <div class="layui-card chat-card">
                 <div class="layui-card-header">
                    <i class="layui-icon layui-icon-dialogue"></i> 课堂互动
                </div>
                <div class="layui-card-body chat-panel">
                    <div class="chat-messages" id="chatMessages">
                        <!-- 消息将显示在这里 -->
                    </div>
                    <div class="chat-input">
                        <input type="text" id="chatInput" class="layui-input" placeholder="输入消息...">
                        <button id="sendChatBtn" class="layui-btn">发送</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.7.2/dist/socket.io.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/hls.js@1.4.10/dist/hls.min.js"></script>
<script>
layui.use(['layer', 'element'], function() {
    var layer = layui.layer;

    const video = document.getElementById('liveVideo');
    const videoPlaceholder = document.getElementById('videoPlaceholder');
    const liveStatus = document.getElementById('liveStatus');
    const reconnectBtn = document.getElementById('reconnectBtn');

    let hls = null;
    let statusInterval = null;
    let isLive = false;
    let socket = null;

    function updateStatus(status, message) {
        liveStatus.innerHTML = `<span class="status-dot ${status}"></span> ${message}`;
    }

    function playVideo(streamUrl) {
        if (Hls.isSupported()) {
            if (hls) {
                hls.destroy();
            }
            hls = new Hls({
                // HLS.js 配置
                lowLatencyMode: true,
                backBufferLength: 90,
                xhrSetup: function(xhr) {
                    // 设置较短的超时时间以快速检测连接问题
                    xhr.timeout = 5000;
                },
                // 添加更多重试选项
                manifestLoadingMaxRetry: 6,
                manifestLoadingRetryDelay: 1000,
                manifestLoadingMaxRetryTimeout: 10000,
                levelLoadingMaxRetry: 6,
                levelLoadingRetryDelay: 1000,
                levelLoadingMaxRetryTimeout: 10000
            });

            // 重置视频元素状态
            video.removeAttribute('src');
            video.load();

            hls.loadSource(streamUrl);
            hls.attachMedia(video);

            hls.on(Hls.Events.MANIFEST_PARSED, function () {
                video.play().catch(e => {
                    console.error("播放失败:", e);
                    layer.msg('浏览器限制自动播放，请手动点击播放按钮', {icon: 0});
                });
                videoPlaceholder.style.display = 'none';
                video.style.display = 'block';
                updateStatus('online', '直播中');
                isLive = true;
            });

            // 添加更多事件监听
            hls.on(Hls.Events.ERROR, function (event, data) {
                console.warn('HLS Error:', data);
                if (data.fatal) {
                    switch(data.type) {
                        case Hls.ErrorTypes.NETWORK_ERROR:
                            console.error('致命的网络错误');
                            // 尝试恢复网络错误
                            hls.startLoad();
                            break;
                        case Hls.ErrorTypes.MEDIA_ERROR:
                            console.error('致命的媒体错误');
                            // 尝试恢复媒体错误
                            hls.recoverMediaError();
                            break;
                        default:
                            // 无法恢复的错误
                            stopVideo();
                            updateStatus('offline', '直播已断开');
                            // 触发立即重新检查
                            setTimeout(checkStreamStatus, 1000);
                            break;
                    }
                }
            });

            // 监听视频错误
            video.addEventListener('error', function(e) {
                console.error('Video Error:', e);
                // 触发立即重新检查
                setTimeout(checkStreamStatus, 1000);
            });

        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
            video.src = streamUrl;
            video.addEventListener('loadedmetadata', function () {
                video.play();
                videoPlaceholder.style.display = 'none';
                video.style.display = 'block';
                updateStatus('online', '直播中');
                isLive = true;
            });

            // Safari的错误处理
            video.addEventListener('error', function(e) {
                console.error('Safari Video Error:', e);
                stopVideo();
                updateStatus('offline', '直播已断开');
                // 触发立即重新检查
                setTimeout(checkStreamStatus, 1000);
            });
        } else {
            layer.msg('您的浏览器不支持HLS播放', {icon: 2});
        }
    }

    function checkStreamStatus() {
        // 通过教师端的API来检查流状态
        $.ajax({
            url: '/teacher/api/desktop_capture/status',
            type: 'GET',
            dataType: 'json',
            timeout: 5000,
            success: function(res) {
                if (res.status === 'success') {
                    const isStreamRunning = res.data.capturing && res.data.ffmpeg_process_running && res.data.mediamtx_running;
                    
                    if (isStreamRunning) {
                        if (res.data.stream_url && res.data.stream_url.hls) {
                            if (!isLive) {
                                console.log('检测到直播已开始，正在加载视频...');
                                updateStatus('loading', '连接中...');
                                playVideo(res.data.stream_url.hls);
                            } else {
                                // 如果已经在播放，检查当前播放的URL是否与服务器返回的一致
                                const currentUrl = hls ? hls.url : video.src;
                                if (currentUrl !== res.data.stream_url.hls) {
                                    console.log('检测到流地址变化，重新加载视频...');
                                    playVideo(res.data.stream_url.hls);
                                }
                            }
                        } else {
                            console.log('直播流地址不存在，等待教师端生成...');
                            updateStatus('loading', '等待流地址...');
                        }
                    } else {
                        if (isLive) {
                            console.log('检测到直播已停止。');
                            stopVideo();
                        }
                    }
                } else {
                    if (isLive) {
                        console.log('状态检查失败，直播可能已中断。');
                        stopVideo();
                    }
                }
            },
            error: function(xhr, status, error) {
                console.error('状态检查失败:', error);
                if (isLive) {
                    // 网络错误时不要立即停止，给予一定的容错时间
                    if (status === 'timeout') {
                        updateStatus('loading', '正在重新连接...');
                    }
                }
            }
        });
    }

    function stopVideo() {
        if (hls) {
            hls.destroy();
            hls = null;
        }
        video.removeAttribute('src'); // 清除src属性
        video.load(); // 重置视频元素
        video.style.display = 'none';
        videoPlaceholder.style.display = 'flex';
        updateStatus('offline', '离线');
        isLive = false;
    }

    // 聊天功能
    function initializeChat() {
        socket = io({ transports: ['websocket'] });
        const room = 'classroom_main';

        socket.on('connect', function() {
            console.log('已连接到聊天服务器');
            socket.emit('join', { room: room });
        });

        socket.on('disconnect', function() {
            console.log('与聊天服务器断开连接');
        });

        socket.on('chat_message', function(data) {
            appendChatMessage(data.user, data.message);
        });
    }

    function sendChatMessage() {
        const input = document.getElementById('chatInput');
        const message = input.value.trim();
        if (message) {
            socket.emit('send_message', { room: 'classroom_main', message: message });
            input.value = '';
        }
    }

    function appendChatMessage(user, message) {
        const messagesContainer = document.getElementById('chatMessages');
        let userClass = 'user';
        if (user === '系统') {
            userClass += ' system';
        } else if (user !== '{{ session.student_name }}') { // 假设当前用户名在session中
            userClass += ' teacher'; // 非自己的消息认为是老师发的
        }

        const messageEl = document.createElement('div');
        messageEl.className = 'chat-message';
        messageEl.innerHTML = `<span class="${userClass}">${user}:</span> <span class="message">${message}</span>`;
        messagesContainer.appendChild(messageEl);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // 添加显示弹幕的调用
        showDanmakuOnVideo(message, user);
    }

    // 添加弹幕显示函数
    function showDanmakuOnVideo(messageText, user) {
        const danmakuContainerElement = document.getElementById('danmakuArea');
        if (!danmakuContainerElement) {
            console.error("Danmaku container #danmakuArea not found!");
            return;
        }
        if (!messageText || messageText.trim() === "") {
            return;
        }

        const danmaku = document.createElement('div');
        danmaku.textContent = `${user}: ${messageText}`; // 显示用户名和消息
        
        danmaku.style.position = 'absolute';
        danmaku.style.right = '0px';
        danmaku.style.whiteSpace = 'nowrap';
        danmaku.style.color = '#fff';
        danmaku.style.fontSize = Math.floor(Math.random() * 10 + 18) + 'px'; // 随机字号 18-27px
        danmaku.style.fontWeight = 'bold';
        danmaku.style.textShadow = '1px 1px 2px rgba(0,0,0,0.7)';
        
        const containerHeight = danmakuContainerElement.clientHeight;
        const danmakuHeightEstimate = parseInt(danmaku.style.fontSize, 10) * 1.2;
        let topPosition = Math.floor(Math.random() * (containerHeight - danmakuHeightEstimate));
        if (topPosition < 0) topPosition = 0;
        danmaku.style.top = topPosition + 'px';

        danmakuContainerElement.appendChild(danmaku);

        danmaku.getBoundingClientRect(); // 强制浏览器重绘

        const animationDuration = Math.random() * 5 + 8; // 8-13秒的随机动画时长
        danmaku.style.transition = `transform ${animationDuration}s linear`;
        
        const moveDistance = danmakuContainerElement.clientWidth + danmaku.offsetWidth;
        danmaku.style.transform = `translateX(-${moveDistance}px)`;

        danmaku.addEventListener('transitionend', function() {
            danmaku.remove();
        });
    }

    // 绑定事件
    reconnectBtn.addEventListener('click', function() {
        layer.msg('正在刷新状态...', {icon: 16, time: 1000, shade: 0.1});
        checkStreamStatus();
    });
    document.getElementById('sendChatBtn').addEventListener('click', sendChatMessage);
    document.getElementById('chatInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            sendChatMessage();
        }
    });

    // 初始加载和定时检查
    checkStreamStatus();
    statusInterval = setInterval(checkStreamStatus, 5000); // 每5秒检查一次
    initializeChat();

    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        clearInterval(statusInterval);
        if (hls) {
            hls.destroy();
        }
        if (socket) {
            socket.emit('leave', {room: 'classroom_main'});
            socket.disconnect();
        }
    });
});
</script>
{% endblock %}