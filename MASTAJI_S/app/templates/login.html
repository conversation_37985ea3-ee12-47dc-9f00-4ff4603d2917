<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧课堂系统 - 登录</title>
    <link rel="stylesheet" href="/static/css/layui.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            font-family: 'Microsoft YaHei', sans-serif;
            padding: 20px;
        }
        .login-container {
            width: 100%;
            max-width: 480px;
            padding: 50px 40px;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(15px);
            animation: slideInUp 0.6s ease-out;
            position: relative;
            overflow: hidden;
        }
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        }
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .login-title {
            text-align: center;
            margin-bottom: 15px;
            font-size: 32px;
            color: #333;
            font-weight: 700;
            position: relative;
        }
        .login-subtitle {
            text-align: center;
            margin-bottom: 40px;
            font-size: 16px;
            color: #666;
            font-weight: 400;
        }
        .login-form {
            margin-top: 30px;
        }
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }
        .form-icon {
            position: absolute;
            left: 20px;
            top: 65%;
            transform: translateY(-50%);
            color: #999;
            font-size: 18px;
            z-index: 2;
        }
        .layui-form-label {
            width: 0;
            padding: 0;
            font-size: 16px;
        }
        .layui-input {
            border-radius: 12px;
            border: 2px solid #e8e8e8;
            height: 55px;
            font-size: 16px;
            transition: all 0.3s ease;
            padding-left: 50px;
            background: #fafafa;
        }
        .layui-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
            background: white;
        }
        .layui-input::placeholder {
            color: #bbb;
            font-size: 15px;
        }
        .login-btn {
            width: 100%;
            height: 55px;
            margin-top: 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            font-size: 18px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }
        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .login-btn:hover::before {
            left: 100%;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }
        .login-btn:active {
            transform: translateY(0);
        }
        .error-message {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
            padding: 15px 20px;
            border-radius: 12px;
            margin-bottom: 25px;
            text-align: center;
            font-size: 15px;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
            animation: shake 0.5s ease-in-out;
        }
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        .back-link {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-size: 15px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }
        .back-link a:hover {
            color: #5a6fd8;
            transform: translateX(-3px);
        }
        @media (max-width: 480px) {
            .login-container {
                padding: 40px 30px;
                margin: 10px;
            }
            .login-title {
                font-size: 28px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-title"> 智慧课堂系统</div>

        {% if error %}
        <div class="error-message">{{ error }}</div>
        {% endif %}

        <form class="layui-form login-form" method="post" action="/login">
            <div class="layui-form-item form-group">
                <label class="layui-form-label" style="width: auto; padding-right: 10px;">用户ID</label>
                <div class="layui-input-block" style="margin-left: 0;">
                    <i class="layui-icon layui-icon-user form-icon"></i>
                    <input type="text" name="user_id" required lay-verify="required" placeholder="请输入学号或教师工号" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item form-group">
                <label class="layui-form-label" style="width: auto; padding-right: 10px;">密码</label>
                <div class="layui-input-block" style="margin-left: 0;">
                    <i class="layui-icon layui-icon-password form-icon"></i>
                    <input type="password" name="password" required lay-verify="required" placeholder="请输入密码" autocomplete="off" class="layui-input">
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block" style="margin-left: 0;">
                    <button class="layui-btn login-btn" lay-submit lay-filter="formDemo" style="display: block; margin: 30px auto 0;">
                        <i class="layui-icon layui-icon-login" style="margin-right: 8px;"></i>
                        登录
                    </button>
                </div>
            </div>
        </form>

        <div class="back-link">
            <a href="/" class="layui-link">
                <i class="layui-icon layui-icon-return"></i>
                返回首页
            </a>
        </div>
    </div>

    <script src="/static/js/layui.js"></script>
    <script>
        layui.use(['form', 'layer'], function(){
            var form = layui.form;
            var layer = layui.layer;

            // 页面加载动画
            document.addEventListener('DOMContentLoaded', function() {
                const container = document.querySelector('.login-container');
                container.style.opacity = '0';
                container.style.transform = 'translateY(30px)';

                setTimeout(() => {
                    container.style.transition = 'all 0.6s ease-out';
                    container.style.opacity = '1';
                    container.style.transform = 'translateY(0)';
                }, 100);
            });

            // 输入框焦点效果
            document.querySelectorAll('.layui-input').forEach(function(input) {
                input.addEventListener('focus', function() {
                    this.parentNode.querySelector('.form-icon').style.color = '#667eea';
                });

                input.addEventListener('blur', function() {
                    this.parentNode.querySelector('.form-icon').style.color = '#999';
                });
            });

            // 表单提交事件
            form.on('submit(formDemo)', function(data){
                // 显示加载动画
                var loadIndex = layer.load(1, {
                    shade: [0.3,'#fff'],
                    content: '<div style="color:#667eea;">登录中...</div>'
                });

                // 表单数据会自动提交
                return true;
            });

            // 错误消息自动消失
            const errorMsg = document.querySelector('.error-message');
            if (errorMsg) {
                setTimeout(() => {
                    errorMsg.style.transition = 'all 0.5s ease-out';
                    errorMsg.style.opacity = '0';
                    errorMsg.style.transform = 'translateY(-10px)';
                    setTimeout(() => {
                        errorMsg.style.display = 'none';
                    }, 500);
                }, 3000);
            }
        });
    </script>
</body>
</html>
