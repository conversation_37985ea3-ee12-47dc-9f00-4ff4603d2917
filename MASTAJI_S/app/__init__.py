from flask import Flask
from config import config
from datetime import datetime
from flask_socketio import SocketIO

# 创建全局 SocketIO 实例
socketio = SocketIO()

# 自定义Jinja2过滤器
def to_option(index):
    """将数字索引转换为字母选项（A, B, C...）"""
    return chr(ord('A') + int(index))

def format_ctime(iso_string):
    """将ISO格式的字符串转换为更友好的 'YYYY-MM-DD HH:MM' 格式"""
    if not iso_string:
        return ""
    try:
        dt_object = datetime.fromisoformat(iso_string)
        return dt_object.strftime('%Y-%m-%d %H:%M')
    except (ValueError, TypeError):
        return iso_string

def create_app(config_name='default'):
    """应用工厂函数"""
    app = Flask(__name__)
    
    # 加载配置
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)
    
    # 初始化 SocketIO
    socketio.init_app(app, cors_allowed_origins="*")
    
    # 注册自定义Jinja2过滤器
    app.jinja_env.filters['to_option'] = to_option
    app.jinja_env.filters['ctime'] = format_ctime
    
    # 初始化数据库
    from app.models.database import init_db
    init_db(app.config['DATABASE_PATH'])
    
    # 注册蓝图
    from app.views.main import main_bp
    from app.views.auth import auth_bp
    from app.views.student import student_bp
    from app.views.teacher import teacher_bp
    from app.views.admin import admin_bp
    from app.views.dashboard import dashboard_bp
    # 导入并注册socketio事件
    from . import socket_events
    socket_events.register_events(socketio)

    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp)
    app.register_blueprint(student_bp)
    app.register_blueprint(teacher_bp)
    app.register_blueprint(admin_bp)
    app.register_blueprint(dashboard_bp)
    
    # 配置静态文件路由
    @app.route('/uploads/<path:filename>')
    def uploaded_file(filename):
        from flask import send_from_directory
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    
    return app, socketio
