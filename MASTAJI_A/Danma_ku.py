import sys
from PyQt5.QtWebSockets import QWebSocket, QWebSocketServer
from PyQt5.QtNetwork import QHostAddress
from PyQt5.QtWidgets import QApplication, QMainWindow, QLabel
from PyQt5.QtCore import QTimer, Qt
from PyQt5.QtGui import QColor


class DanmakuTeacherApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.initUI()
        self.initWebSocketServer()
        self.websocket_clients = []  # 存储学生端的连接
        self.danmaku_labels = []  # 存储所有弹幕标签
        self.danmaku_speed = 5  # 弹幕移动速度（像素/帧）

    def initUI(self):
        # 设置窗口标题
        self.setWindowTitle("教师端 - 弹幕显示")

        # 设置窗口为全屏
        # self.showFullScreen()

        # 设置窗口背景透明
        self.setWindowFlags(Qt.FramelessWindowHint |
                           Qt.Tool | self.windowFlags())
        self.setAttribute(Qt.WA_TranslucentBackground)
       

        # 定时器用于更新弹幕位置
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_danmaku)
        self.timer.start(30)  # 启动定时器

    def initWebSocketServer(self):
        # 初始化 WebSocket 服务器
        self.websocket_server = QWebSocketServer(
            "DanmakuServer", QWebSocketServer.NonSecureMode)
        if self.websocket_server.listen(QHostAddress.Any, 8000):
            print("WebSocket 服务器已启动，监听端口 8000")
        else:
            print("无法启动 WebSocket 服务器")

        # 连接信号槽
        self.websocket_server.newConnection.connect(self.on_new_connection)

    def on_new_connection(self):
        # 处理新连接
        client = self.websocket_server.nextPendingConnection()
        self.websocket_clients.append(client)
        client.textMessageReceived.connect(self.on_message_received)
        client.disconnected.connect(self.on_client_disconnected)

    def on_message_received(self, message):
        # 创建弹幕标签
        self.create_danmaku(message)

    def on_client_disconnected(self):
        # 处理客户端断开连接
        client = self.sender()
        if client in self.websocket_clients:
            self.websocket_clients.remove(client)

    def create_danmaku(self, text):
        # 创建弹幕标签
        label = QLabel(text, self)
        label.setStyleSheet(
            "color: red; font-size: 40px; background-color: rgba(0, 0, 0, 0);")
        label.adjustSize()  # 调整标签大小以适应文本
        y = self.get_random_y_position(label.height())  # 随机生成 Y 坐标
        label.move(self.width(), y)  # 初始位置在窗口右侧
        label.show()  # 确保标签显示
        self.danmaku_labels.append(label)

    def get_random_y_position(self, label_height):
        # 随机生成弹幕的 Y 坐标
        import random
        return random.randint(0, self.height() - label_height)

    def update_danmaku(self):
        # 更新弹幕位置
        for label in self.danmaku_labels:
            x = label.x() - self.danmaku_speed  # 向左移动
            y = label.y()
            label.move(x, y)

            # 如果弹幕移出窗口，移除标签
            if x + label.width() < 0:
                label.deleteLater()
                self.danmaku_labels.remove(label)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DanmakuTeacherApp()
    window.showFullScreen()
    sys.exit(app.exec_())
