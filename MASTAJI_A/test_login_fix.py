#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录修复的脚本
"""

import sys
import requests
from PyQt5.QtWidgets import QApplication

# 添加当前目录到Python路径
sys.path.append('.')

def test_teacher_info_passing():
    """测试教师信息传递"""
    print("=== 测试教师信息传递 ===")
    
    # 模拟session对象
    session = requests.Session()
    
    # 模拟teacher_info
    teacher_info = {
        'user_id': 'T001',
        'teacher_name': '测试教师',
        'session': session,
        'server_url': 'http://localhost:5000'
    }
    
    print(f"原始teacher_info: {teacher_info}")
    
    # 测试Select_People的初始化逻辑
    from Select_People import RandomStudentPicker
    
    # 创建QApplication（避免QWidget错误）
    app = QApplication(sys.argv)
    
    try:
        # 测试1: 传入完整的teacher_info和course_id
        print("\n测试1: 传入完整的teacher_info和course_id")
        picker = RandomStudentPicker(teacher_info=teacher_info, current_course_id='C001')
        print(f"  teacher_id: {picker.teacher_id}")
        print(f"  session存在: {picker.session is not None}")
        print(f"  current_course_id: {picker.current_course_id}")
        print(f"  server_url: {picker.server_url}")
        
        # 测试2: 只传入teacher_info，不传course_id
        print("\n测试2: 只传入teacher_info，不传course_id")
        picker2 = RandomStudentPicker(teacher_info=teacher_info)
        print(f"  teacher_id: {picker2.teacher_id}")
        print(f"  session存在: {picker2.session is not None}")
        print(f"  current_course_id: {picker2.current_course_id}")
        
        # 测试3: 不传入任何参数
        print("\n测试3: 不传入任何参数")
        picker3 = RandomStudentPicker()
        print(f"  teacher_id: {picker3.teacher_id}")
        print(f"  session: {picker3.session}")
        print(f"  current_course_id: {picker3.current_course_id}")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

def test_dashboard_initialization():
    """测试Dashboard初始化"""
    print("\n=== 测试Dashboard初始化 ===")
    
    from dashboard import Dashboard
    
    # 模拟teacher_info
    teacher_info = {
        'user_id': 'T001',
        'teacher_name': '测试教师',
        'session': requests.Session(),
        'server_url': 'http://localhost:5000'
    }
    
    try:
        # 创建QApplication
        app = QApplication(sys.argv)
        
        # 测试传入teacher_info的Dashboard
        print("创建带teacher_info的Dashboard...")
        dashboard = Dashboard(teacher_info=teacher_info)
        print(f"  teacher_name: {dashboard.teacher_name}")
        print(f"  teacher_info: {dashboard.teacher_info}")
        print(f"  session存在: {dashboard.session is not None}")
        
        print("Dashboard初始化测试完成")
        app.quit()
        
    except Exception as e:
        print(f"Dashboard测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_teacher_info_passing()
    test_dashboard_initialization()
