# 登录系统修复说明

## 问题描述

修复前的问题：
1. 直接运行 `select_people.py` 会出现登录错误弹窗
2. 子模块无法获取已登录的教师信息
3. 信息传递链不完整，导致课堂工具无法正常工作

## 解决方案

### 1. 创建统一的登录状态管理器

新增了 `login_manager.py` 文件，实现了：
- **单例模式**：确保全局只有一个登录管理器实例
- **会话持久化**：登录状态保存到 `session_data.pkl` 文件
- **自动恢复**：程序启动时自动恢复上次的登录状态
- **会话验证**：定期验证登录状态是否有效
- **统一接口**：为所有子模块提供统一的登录验证接口

### 2. 修改子模块的登录验证逻辑

#### Select_People.py 修改：
- 导入登录管理器：`from login_manager import get_login_manager`
- 优先使用传入的 `teacher_info`，否则从登录管理器获取
- 新增 `checkLoginAndLoadData()` 方法，统一处理登录验证
- 未登录时自动弹出登录窗口

#### Mastaji.py 修改：
- 导入登录管理器并在初始化时检查登录状态
- 未登录时调用 `require_login_and_restart()` 方法
- 确保所有子模块都能获取到正确的教师信息

### 3. 完善信息传递机制

#### main.py 修改：
- 启动时检查是否已有有效登录会话
- 如果已登录，直接进入dashboard
- 登录成功后同时更新登录管理器状态

#### dashboard.py 修改：
- 同步更新登录管理器状态
- 确保从dashboard启动的子模块能获取正确信息

### 4. 配置文件完善

在 `config.json` 中添加了：
```json
"teacher": {
    "default_password": "123"
}
```

## 修复效果

### 1. 正常流程（推荐）
```
main.py → login_window → dashboard → mastaji.py → 子模块
```
- 所有步骤都能正确传递教师信息
- 登录状态会自动保存和恢复

### 2. 直接运行子模块
- **Select_People.py**：现在可以直接运行，会自动检查登录状态
- **Mastaji.py**：现在可以直接运行，会自动检查登录状态
- 未登录时会自动弹出登录窗口

### 3. 登录状态持久化
- 登录一次后，重启程序会自动恢复登录状态
- 会话有效期为1小时，过期后需要重新登录
- 会话文件：`session_data.pkl`

## 测试验证

运行测试脚本验证修复效果：
```bash
python test_login_system.py
```

测试内容包括：
1. 登录管理器基本功能
2. 登录状态持久化
3. 直接运行Select_People
4. 直接运行Mastaji

## 使用说明

### 正常使用流程
1. 运行 `python main.py` 启动主程序
2. 在登录窗口输入教师账号密码
3. 登录成功后进入dashboard
4. 从dashboard进入课堂使用各种工具

### 直接使用子模块
1. 可以直接运行 `python Select_People.py`
2. 如果未登录，会自动弹出登录窗口
3. 登录成功后即可正常使用

### 登录状态管理
- 登录状态会自动保存，下次启动时自动恢复
- 如需清除登录状态，删除 `session_data.pkl` 文件
- 会话超时时间为1小时，可在 `login_manager.py` 中修改

## 技术细节

### 登录管理器核心功能
```python
from login_manager import get_login_manager

# 获取登录管理器实例
login_manager = get_login_manager()

# 检查登录状态
if login_manager.is_logged_in:
    teacher_info = login_manager.get_teacher_info()
    session = login_manager.get_session()

# 要求登录
if login_manager.require_login(parent_widget):
    # 登录成功
    pass
```

### 会话文件结构
```python
{
    'teacher_info': {
        'user_id': 'T002',
        'teacher_name': '李教授',
        'server_url': 'http://localhost:8080'
    },
    'login_time': datetime.now(),
    'session_cookies': {...},
    'server_url': 'http://localhost:8080'
}
```

## 注意事项

1. **会话安全**：会话文件包含登录凭据，请妥善保管
2. **网络连接**：登录验证需要连接到后端服务器
3. **权限管理**：确保只有授权用户才能访问课堂工具
4. **错误处理**：网络异常时会自动降级处理

## 故障排除

### 问题1：直接运行子模块仍然提示需要登录
- 检查 `session_data.pkl` 文件是否存在
- 检查网络连接是否正常
- 尝试删除会话文件重新登录

### 问题2：登录状态无法保存
- 检查文件写入权限
- 检查磁盘空间是否充足
- 查看控制台错误信息

### 问题3：会话验证失败
- 检查后端服务器是否正常运行
- 检查服务器地址配置是否正确
- 尝试重新登录

通过以上修复，现在整个系统的登录状态管理更加完善和可靠，确保了信息传递链的完整性。
