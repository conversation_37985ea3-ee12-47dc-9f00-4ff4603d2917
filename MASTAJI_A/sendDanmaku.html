<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学生端弹幕发送</title>
</head>

<body>
    <h1>发送弹幕</h1>
    <input type="text" id="danmakuInput" placeholder="输入弹幕内容">
    <button onclick="sendDanmaku()">发送</button>

    <script>
        const ws = new WebSocket("ws://localhost:8000/danmaku");

        function sendDanmaku() {
            const input = document.getElementById("danmakuInput");
            const message = input.value;
            if (message) {
                ws.send(message);
                input.value = "";
            }
        }  
    </script>
</body>

</html>