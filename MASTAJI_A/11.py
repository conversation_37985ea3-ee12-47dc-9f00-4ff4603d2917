from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtWebEngineWidgets import QWebEngineView
from PyQt5.QtCore import QUrl, Qt
import json
import sys
import os

class Groupscreen(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("多屏互动")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)

        # 创建Web视图
        self.webview = QWebEngineView()
        layout.addWidget(self.webview)
       
        # 加载视频URL
        video_urls = self._load_video_urls("database/video_urls.json")
        if not video_urls:
            self.webview.setHtml("<h1 style='color:red'>错误: 无法加载视频URL</h1>")
        else:
            html_content = self._generate_html(video_urls)
            self.webview.setHtml(html_content)

    def _load_video_urls(self, json_file):
        """加载视频URL"""
        if not os.path.exists(json_file):
            return []
            
        try:
            with open(json_file, 'r', encoding='utf-8') as file:
                data = json.load(file)
                if not isinstance(data.get("video_urls"), list):
                    raise ValueError("video_urls 必须是列表")
                return data.get("video_urls", [])[:9]  # 最多支持9个视频
        except Exception as e:
            print(f"加载错误: {e}")
            return []

    def _generate_html(self, video_urls):
        """生成HTML内容"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>分屏播放器</title>
            <style>
                body {{ margin: 0; padding: 0; height: 100vh; overflow: hidden; }}
                .container {{ 
                    display: grid; 
                    width: 100%;
                    height: calc(100% - 50px);
                    gap: 5px;
                    background: #333;
                }}
                .stream {{ 
                    background: #000; 
                    min-height: 0;
                    min-width: 0;
                }}
                iframe {{ 
                    width: 100%;
                    height: 100%;
                    display: block;
                }}
                .controls {{
                    padding: 10px;
                    background: #444;
                    text-align: center;
                }}
                button {{
                    padding: 8px 15px;
                    margin: 0 5px;
                    background: #555;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    cursor: pointer;
                }}
                button:hover {{ background: #666; }}
            </style>
        </head>
        <body>
            <div class="container" id="container">
                {"".join(f'<div class="stream"><iframe src="{url}" allowfullscreen></iframe></div>' 
                        for url in video_urls)}
            </div>
            <div class="controls">
                <button onclick="setLayout(1)">1屏</button>
                <button onclick="setLayout(2)">2屏</button>
                <button onclick="setLayout(3)">3屏</button>
                <button onclick="setLayout(4)">4屏</button>
                <button onclick="setLayout(6)">6屏</button>
                <button onclick="setLayout(9)">9屏</button>
            </div>
            <script>
                function setLayout(count) {{
                    const container = document.getElementById('container');
                    const streams = container.getElementsByClassName('stream');
                    
                    // 设置网格
                    const cols = Math.ceil(Math.sqrt(count));
                    const rows = Math.ceil(count / cols);
                    container.style.gridTemplateColumns = `repeat(${cols}, 1fr)`;
                    container.style.gridTemplateRows = `repeat(${rows}, 1fr)`;
                    
                    // 显示/隐藏流
                    for (let i = 0; i < streams.length; i++) {{
                        if (i < count) {{
                            streams[i].style.display = 'block';
                            streams[i].style.gridColumn = (i % cols) + 1;
                            streams[i].style.gridRow = Math.floor(i / cols) + 1;
                        }} else {{
                            streams[i].style.display = 'none';
                        }}
                    }}
                }}
                setLayout(4); // 默认4分屏
                
                // 窗口大小变化时重新布局
                window.onresize = function() {{
                    const currentLayout = parseInt(document.querySelector('button.active')?.textContent) || 4;
                    setLayout(currentLayout);
                }};
                
                // 添加活动按钮样式
                document.querySelectorAll('button').forEach(btn => {{
                    btn.addEventListener('click', function() {{
                        document.querySelectorAll('button').forEach(b => b.classList.remove('active'));
                        this.classList.add('active');
                    }});
                }});
            </script>
        </body>
        </html>
        """

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = Groupscreen()
    window.show()
    sys.exit(app.exec_())