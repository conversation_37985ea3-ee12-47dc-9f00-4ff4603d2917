class MyCss():
    # def __init__(self):
    #     super().__init__()

    #主窗口背景主色调
    mainBgcolor="background-color: rgb(9, 9, 9)"
    # 主窗口背景主色调
    mainBgcolora = "background-color: rgba(9, 9, 9,180);border-radius:5px;border:1px solid rgba(150, 150, 150,40);"
    # 主窗口背景主色调
    mainBackground = "background-color: rgba(0, 0, 0,2)"
    menuBackground = "background-color: rgba(0, 0, 0,50);border-radius:5px;border:1px solid rgba(150, 150, 150,40);"
    # 主窗口背景主色调
    mainBgcolorc = "background-color: rgba(19, 19, 19,250)"
    # 按钮样式
    menuIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/setGN0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/setGN1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/setGN482.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 按钮样式
    closeIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/DClose0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/DClose1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/DClose2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    BoxIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/Box0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/Box1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/Box2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 三角样式
    triangleCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/triangle0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/triangle1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/triangle2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 按钮样式
    circularIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/circular0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/circular1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/circular2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 撤消按钮样式
    revokeIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/revoke0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/revoke1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/revoke2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    
    ellipseIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/ellipse0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/ellipse1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/ellipse2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 按钮样式
    lineIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/line0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/line1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/line2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 按钮样式
    colorIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/color0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/color1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/color2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 按钮样式
    plottingIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-color: rgba(85,85,85,0);background-image: url(images/plotting0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/plotting1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/plotting2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 按钮样式
    setupIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/setup0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/setup1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/setup2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 擦除样式
    erasureIconCss="QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/erasure0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/erasure1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/erasure2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 清除样式
    ClearIconCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/Clear0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/Clear1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/Clear2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 截屏样式
    ScreenshotCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/capture_screen.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/capture_screen1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/capture_screen2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
   
    # 录屏样式
    recordCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/record.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/record1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/record2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # PPT样式
    pptCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/ppt.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/ppt1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/ppt2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    ppt_rightCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/ppt_right.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/ppt_right1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/ppt_right2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    ppt_leftCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/ppt_left.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/ppt_left1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/ppt_left2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    ppt_EndCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/ppt_End.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/ppt_End1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/ppt_End2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    
    # 按钮样式
    butBCss = "QPushButton{background-color: rgba(85,85,85,180);border-radius:5px;border:1px solid rgba(105,105,105,150);font-weight: bold;font: 200 10pt \"微软雅黑\";color:rgba(255,255,255,150)}QPushButton:hover{background-color: rgba(85,85,85,180);border-radius:5px;border:1px solid rgba(105,105,105,100);font-weight: bold;font: 75 10pt \"微软雅黑\";color:rgba(250,114,5,200)}QPushButton:pressed{background-color: rgba(85,85,85,180);border-radius:5px;border:1px solid rgba(105,105,105,50);font-weight: bold;font: 75 10pt \"微软雅黑\";color:rgba(6,178,168,200)}"
    #按钮样式
    butCss="QPushButton{background-color: rgba(85,85,85,0);border-radius:5px;border:1px solid rgba(105,105,105,150);font: 75 10pt \"微软雅黑\";color:rgba(255,255,255,150)}QPushButton:hover{background-color: rgba(85,85,85,100);border-radius:5px;border:1px solid rgba(105,105,105,100);font: 75 10pt \"微软雅黑\";color:rgba(250,114,5,200)}QPushButton:pressed{background-color: rgba(85,85,85,100);border-radius:5px;border:1px solid rgba(105,105,105,50);font: 75 10pt \"微软雅黑\";color:rgba(6,178,168,200)}"
    #按钮样式
    butsCss = "QPushButton{background-color: rgba(85,85,85,0);border-radius:5px;border:1px solid rgba(253, 167, 0,150);font: 75 10pt \"微软雅黑\";color:rgba(253, 167, 0,150)}QPushButton:hover{background-color: rgba(85,85,85,100);border-radius:5px;border:1px solid rgba(253, 167, 0,150);font: 75 10pt \"微软雅黑\";color:rgba(253, 167, 0,150)}QPushButton:pressed{background-color: rgba(85,85,85,100);border-radius:5px;border:1px solid rgba(253, 167, 0,150);font: 75 10pt \"微软雅黑\";color:rgba(253, 167, 0,150)}"
    #数组按钮样式
    szbutCss = "QPushButton{background-color: rgba(85,85,85,80);border-radius:8px;border:1px solid rgba(105,105,105,150);font: 87 16pt \"微软雅黑\";color:rgba(255,255,255,150)}QPushButton:hover{background-color: rgba(85,85,85,100);border-radius:5px;border:1px solid rgba(105,105,105,100);font: 75 16pt \"微软雅黑\";color:rgba(250,114,5,200)}QPushButton:pressed{background-color: rgba(85,85,85,100);border-radius:5px;border:1px solid rgba(105,105,105,50);font: 75 16pt \"微软雅黑\";color:rgba(250,114,5,200)}"
   
    # 聚光灯按钮样式
    spotlightCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/spotlight0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/spotlight1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/spotlight2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    # 放大镜按钮样式
    MagnifyCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/Magnify.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/Magnify1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/Magnify2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"

    #关闭按钮样式
    chCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/DClose0.png);background-position:center;background-repeat:no-repeat;border-radius:0px;border:0px solid rgba(105,105,105,0);}QPushButton:hover{background-image: url(images/DClose1.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}QPushButton:pressed{background-image: url(images/DClose2.png);border-radius:0px;border:0px solid rgba(105,105,105,0);background-position:center;background-repeat:no-repeat}"
    
    #关闭按钮样式
    tuichuCss = "QPushButton{background-color: rgba(85,85,85,0);background-image: url(images/huifu1.png);border-radius:0px;border:0px solid rgba(105,105,105,250);}QPushButton:hover{background-image: url(images/huifu2.png);border-radius:0px;border:0px solid rgba(105,105,105,250)}QPushButton:pressed{background-image: url(images/huifu3.png);border-radius:0px;border:0px solid rgba(105,105,105,250)}"
    #关闭按钮样式
    closebutCss = "QPushButton{background-image: url(images/close.png);border-radius:0px;border:0px solid rgba(105,105,105,250);}QPushButton:hover{background-image: url(images/close1.png);border-radius:0px;border:0px solid rgba(105,105,105,250)}QPushButton:pressed{background-image: url(images/close2.png);border-radius:0px;border:0px solid rgba(105,105,105,250)}"













