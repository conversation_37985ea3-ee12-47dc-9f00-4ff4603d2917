import sys
import random
import json
import os
import requests
from PyQt5.QtWidgets import QApp<PERSON>, QWidget, QGridLayout, QPushButton, QLabel, QMessageBox, QComboBox, QHBoxLayout
from PyQt5.QtGui import QPixmap, QPainter, QColor, QFont
from PyQt5.QtCore import Qt, QTimer, QPoint


class RandomStudentPicker(QWidget):
    def __init__(self, teacher_info=None, current_course_id=None):
        super().__init__()
        # 加载配置
        self.config = self.loadConfig()
        self.server_url = self.config['server']['url']

        # 使用传入的教师信息
        if teacher_info:
            self.teacher_id = teacher_info.get('user_id')
            self.session = teacher_info.get('session')
            self.server_url = teacher_info.get('server_url', self.server_url)
        else:
            self.teacher_id = None
            self.session = None

        # 当前课程ID - 如果传入则直接使用，否则需要选择
        self.current_course_id = current_course_id
        self.students = []
        self.current_index = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.updateStudent)
        self.fireworks = []
        self.firework_timer = QTimer()
        self.firework_timer.timeout.connect(self.updateFireworks)
        self.initUI()

        # 如果已有session，直接加载课程或学生；否则尝试登录
        if self.session:
            print("使用已有的登录session，跳过登录步骤")
            if self.current_course_id:
                # 直接加载当前课程的学生
                self.loadStudentsFromServer()
            else:
                # 加载课程列表供选择
                self.loadCourseSchedules()
        else:
            print("没有session，需要执行登录")
            self.loginAndLoadCourses()

    def loadConfig(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return 0

    def initUI(self):
        self.setWindowTitle('课堂随机选人工具')
        self.setGeometry(600, 200, 450, 500)
        self.setWindowFlags(Qt.Tool | self.windowFlags())
        layout = QGridLayout()

        # 课程选择区域 - 只有在没有指定课程时才显示
        if not self.current_course_id:
            course_layout = QHBoxLayout()
            course_label = QLabel('选择课程:', self)
            self.courseComboBox = QComboBox(self)
            self.courseComboBox.currentTextChanged.connect(self.onCourseChanged)
            course_layout.addWidget(course_label)
            course_layout.addWidget(self.courseComboBox)

            course_widget = QWidget()
            course_widget.setLayout(course_layout)
            layout.addWidget(course_widget, 0, 0, 1, 2)

            # 头像显示区域
            self.avatarLabel = QLabel(self)
            self.avatarLabel.setAlignment(Qt.AlignCenter)
            self.avatarLabel.setMinimumHeight(150)
            self.avatarLabel.setStyleSheet("border: 1px solid gray;")
            layout.addWidget(self.avatarLabel, 1, 0, 1, 2)

            # 学生信息显示
            self.nameLabel = QLabel('请先选择课程', self)
            self.nameLabel.setAlignment(Qt.AlignCenter)
            self.nameLabel.setStyleSheet("font-size: 16px; font-weight: bold;")
            layout.addWidget(self.nameLabel, 2, 0, 1, 2)

            self.idLabel = QLabel('', self)
            self.idLabel.setAlignment(Qt.AlignCenter)
            self.idLabel.setStyleSheet("font-size: 14px; color: gray;")
            layout.addWidget(self.idLabel, 3, 0, 1, 2)

            # 控制按钮
            self.startButton = QPushButton('开始随机选人', self)
            self.startButton.clicked.connect(self.startRolling)
            self.startButton.setEnabled(False)
            layout.addWidget(self.startButton, 4, 0)

            self.stopButton = QPushButton('停止', self)
            self.stopButton.clicked.connect(self.stopRolling)
            self.stopButton.setEnabled(False)
            layout.addWidget(self.stopButton, 4, 1)

            # 刷新按钮
            self.refreshButton = QPushButton('刷新课程列表', self)
            self.refreshButton.clicked.connect(self.loginAndLoadCourses)
            layout.addWidget(self.refreshButton, 5, 0, 1, 2)
        else:
            # 直接显示学生选择界面，不显示课程选择器
            # 头像显示区域
            self.avatarLabel = QLabel(self)
            self.avatarLabel.setAlignment(Qt.AlignCenter)
            self.avatarLabel.setMinimumHeight(150)
            self.avatarLabel.setStyleSheet("border: 1px solid gray;")
            layout.addWidget(self.avatarLabel, 0, 0, 1, 2)

            # 学生信息显示
            self.nameLabel = QLabel('正在加载学生数据...', self)
            self.nameLabel.setAlignment(Qt.AlignCenter)
            self.nameLabel.setStyleSheet("font-size: 16px; font-weight: bold;")
            layout.addWidget(self.nameLabel, 1, 0, 1, 2)

            self.idLabel = QLabel('', self)
            self.idLabel.setAlignment(Qt.AlignCenter)
            self.idLabel.setStyleSheet("font-size: 14px; color: gray;")
            layout.addWidget(self.idLabel, 2, 0, 1, 2)

            # 控制按钮
            self.startButton = QPushButton('开始随机选人', self)
            self.startButton.clicked.connect(self.startRolling)
            self.startButton.setEnabled(False)
            layout.addWidget(self.startButton, 3, 0)

            self.stopButton = QPushButton('停止', self)
            self.stopButton.clicked.connect(self.stopRolling)
            self.stopButton.setEnabled(False)
            layout.addWidget(self.stopButton, 3, 1)

        self.setLayout(layout)

    def loginAndLoadCourses(self):
        """登录并加载课程列表"""
        # 如果已有session，直接使用；否则执行登录
        if self.session:
            print("已有session，跳过登录步骤")
        elif self.performLogin():
            print("登录成功")
        else:
            # 登录失败
            self.nameLabel.setText('登录失败，无法获取数据')
            self.startButton.setEnabled(False)
            return

        # 加载课程或学生数据
        if self.current_course_id:
            # 直接加载当前课程的学生
            self.loadStudentsFromServer()
        else:
            # 加载课程列表供选择
            self.loadCourseSchedules()

    def performLogin(self):
        """执行登录操作"""
        try:
            self.session = requests.Session()

            # 先获取登录页面
            response = self.session.get(f"{self.server_url}/login",
                                      timeout=self.config['server']['timeout'])
            if response.status_code != 200:
                raise Exception(f"无法访问登录页面: {response.status_code}")

            # 提交登录表单
            login_data = {
                'user_id': self.teacher_id,
                'password': self.config['teacher']['default_password']
            }
            response = self.session.post(f"{self.server_url}/login",
                                       data=login_data,
                                       timeout=self.config['server']['timeout'])

            # 检查是否登录成功
            if response.status_code == 200 and 'error' not in response.text.lower():
                return True
            else:
                raise Exception("登录失败：用户名或密码错误")

        except requests.exceptions.RequestException as e:
            QMessageBox.warning(self, '网络错误', f'登录时网络连接失败: {str(e)}')
            return False
        except Exception as e:
            QMessageBox.warning(self, '登录错误', f'登录失败: {str(e)}')
            return False

    def loadCourseSchedules(self):
        """加载课程安排列表"""
        if not self.session:
            QMessageBox.warning(self, '错误', '未登录，无法获取课程列表')
            self.nameLabel.setText('未登录，无法获取数据')
            return

        try:
            response = self.session.get(f"{self.server_url}/teacher/get_course_schedules",
                                      timeout=self.config['server']['timeout'])

            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('status') == 'success':
                        schedules = data.get('course_schedules', [])

                        # 只有在没有指定课程时才需要课程选择器
                        if hasattr(self, 'courseComboBox'):
                            self.courseComboBox.clear()
                            self.courseComboBox.addItem("请选择课程", None)

                            for schedule in schedules:
                                # 转换星期数字为中文
                                day_names = {1: '周一', 2: '周二', 3: '周三', 4: '周四', 5: '周五', 6: '周六', 7: '周日'}
                                day_name = day_names.get(schedule['day_of_week'], f"周{schedule['day_of_week']}")

                                display_text = f"{schedule['course_name']} - {schedule['class_name']} ({day_name} {schedule['start_time']}-{schedule['end_time']})"
                                self.courseComboBox.addItem(display_text, schedule['id'])

                        if len(schedules) > 0:
                            self.nameLabel.setText(f'已加载 {len(schedules)} 门课程')
                        else:
                            self.nameLabel.setText('暂无课程安排')
                    else:
                        QMessageBox.warning(self, '错误', f"获取课程列表失败: {data.get('message', '未知错误')}")
                        self.nameLabel.setText('获取课程列表失败')
                except json.JSONDecodeError:
                    QMessageBox.warning(self, '错误', '服务器返回数据格式错误')
                    self.nameLabel.setText('服务器数据格式错误')
            else:
                QMessageBox.warning(self, '错误', f'服务器响应错误: {response.status_code}')
                self.nameLabel.setText(f'服务器响应错误: {response.status_code}')

        except requests.exceptions.RequestException as e:
            QMessageBox.warning(self, '网络错误', f'无法连接到服务器: {str(e)}')
            self.nameLabel.setText('网络连接失败')
        except Exception as e:
            QMessageBox.warning(self, '错误', f'加载课程列表失败: {str(e)}')
            self.nameLabel.setText('加载课程列表失败')



    def onCourseChanged(self):
        """课程选择改变时的处理"""
        if hasattr(self, 'courseComboBox'):
            current_data = self.courseComboBox.currentData()
            if current_data:
                self.current_course_id = current_data
                self.loadStudentsFromServer()
            else:
                self.current_course_id = None
                self.students = []
                self.startButton.setEnabled(False)
                self.nameLabel.setText('请先选择课程')
                self.idLabel.setText('')
                self.avatarLabel.clear()

    def loadStudentsFromServer(self):
        """从服务器加载学生数据"""
        if not self.current_course_id or not self.session:
            return

        try:
            response = self.session.get(
                f"{self.server_url}/teacher/class/{self.current_course_id}/students/for_picker",
                timeout=self.config['server']['timeout']
            )

            if response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('success'):
                        self.students = data.get('students', [])
                        if self.students:
                            self.nameLabel.setText(f'已加载 {len(self.students)} 名学生')
                            self.startButton.setEnabled(True)
                            self.idLabel.setText('点击"开始随机选人"开始')
                            self.avatarLabel.clear()
                        else:
                            self.nameLabel.setText('该课程暂无学生')
                            self.startButton.setEnabled(False)
                            self.idLabel.setText('')
                            self.avatarLabel.clear()
                    else:
                        error_msg = data.get('message', '未知错误')
                        self.nameLabel.setText(f'获取学生列表失败: {error_msg}')
                        self.startButton.setEnabled(False)
                        print(f"服务器错误: {error_msg}")
                except json.JSONDecodeError as e:
                    self.nameLabel.setText('服务器返回数据格式错误')
                    self.startButton.setEnabled(False)
                    print(f"JSON解析错误: {e}")
            else:
                self.nameLabel.setText(f'服务器响应错误: {response.status_code}')
                self.startButton.setEnabled(False)
                print(f"HTTP错误: {response.status_code}")

        except requests.exceptions.RequestException as e:
            self.nameLabel.setText('网络连接失败')
            self.startButton.setEnabled(False)
            print(f'网络错误: {str(e)}')
        except Exception as e:
            self.nameLabel.setText('加载学生列表失败')
            self.startButton.setEnabled(False)
            print(f'未知错误: {str(e)}')

    def startRolling(self):
        if not self.students:
            QMessageBox.warning(self, '错误', '学生名单为空，请检查网络连接或课程设置！')
            return
        self.startButton.setEnabled(False)
        self.stopButton.setEnabled(True)
        self.timer.start(100)  # 每100毫秒更新一次

    def stopRolling(self):
        self.timer.stop()
        self.startButton.setEnabled(True)
        self.stopButton.setEnabled(False)
        selected_student = self.students[self.current_index]
        self.nameLabel.setText(f'选中学生: {selected_student["name"]}')
        self.idLabel.setText(f'学号: {selected_student["id"]}')
        self.displayAvatar(selected_student.get("avatar", "default.png"))
        self.startFireworks()

    def updateStudent(self):
        self.current_index = random.randint(0, len(self.students) - 1)
        current_student = self.students[self.current_index]
        self.nameLabel.setText(f'正在选择: {current_student["name"]}')
        self.idLabel.setText(f'学号: {current_student["id"]}')
        self.displayAvatar(current_student.get("avatar", "default.png"))

    def displayAvatar(self, avatar_filename):
        """显示头像，如果文件不存在则显示默认头像"""
        avatar_path = os.path.join("database", avatar_filename)

        # 如果头像文件不存在，创建一个默认头像
        if not os.path.exists(avatar_path):
            pixmap = self.createDefaultAvatar()
        else:
            pixmap = QPixmap(avatar_path)
            if pixmap.isNull():
                pixmap = self.createDefaultAvatar()

        avatar_size = 150
        self.avatarLabel.setPixmap(pixmap.scaled(avatar_size, avatar_size, Qt.KeepAspectRatio, Qt.SmoothTransformation))

    def createDefaultAvatar(self):
        """创建默认头像"""
        avatar_size = 150
        pixmap = QPixmap(avatar_size, avatar_size)
        pixmap.fill(QColor(200, 200, 200))

        painter = QPainter(pixmap)
        painter.setPen(QColor(100, 100, 100))
        painter.setFont(QFont("Arial", 24))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "👤")
        painter.end()

        return pixmap

    def startFireworks(self):
        self.fireworks = []
        self.firework_timer.start(50)  # 每50毫秒更新一次烟花

    def updateFireworks(self):
        if len(self.fireworks) < 20:  # 最多显示20个烟花
            self.fireworks.append(Firework(self.width(), self.height()))
        for firework in self.fireworks:
            firework.update()
        self.update()

    def paintEvent(self, event):
        super().paintEvent(event)
        painter = QPainter(self)
        for firework in self.fireworks:
            firework.draw(painter)


class Firework:
    def __init__(self, width, height):
        self.x = random.randint(0, width)
        self.y = random.randint(0, height)
        self.color = QColor(random.randint(0, 255), random.randint(
            0, 255), random.randint(0, 255))
        self.radius = random.randint(5, 20)
        self.life = random.randint(10, 30)

    def update(self):
        self.life -= 1

    def draw(self, painter):
        if self.life > 0:
            painter.setPen(Qt.NoPen)
            painter.setBrush(self.color)
            painter.drawEllipse(QPoint(self.x, self.y),
                                self.radius, self.radius)