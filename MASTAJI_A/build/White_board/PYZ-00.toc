('/media/mastaji/文件/小马助教开发/MASTAJI_A/build/White_board/PYZ-00.pyz',
 [('Ppt_Control',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/Ppt_Control.py',
   'PYMODULE'),
  ('PyQt5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/__init__.py',
   'PYMODULE'),
  ('SysCss', '/media/mastaji/文件/小马助教开发/MASTAJI_A/SysCss.py', 'PYMODULE'),
  ('_compat_pickle', '/usr/local/lib/python3.12/_compat_pickle.py', 'PYMODULE'),
  ('_compression', '/usr/local/lib/python3.12/_compression.py', 'PYMODULE'),
  ('_py_abc', '/usr/local/lib/python3.12/_py_abc.py', 'PYMODULE'),
  ('_pydatetime', '/usr/local/lib/python3.12/_pydatetime.py', 'PYMODULE'),
  ('_pydecimal', '/usr/local/lib/python3.12/_pydecimal.py', 'PYMODULE'),
  ('_pyi_rth_utils',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyInstaller/fake-modules/_pyi_rth_utils/qt.py',
   'PYMODULE'),
  ('_strptime', '/usr/local/lib/python3.12/_strptime.py', 'PYMODULE'),
  ('_threading_local',
   '/usr/local/lib/python3.12/_threading_local.py',
   'PYMODULE'),
  ('argparse', '/usr/local/lib/python3.12/argparse.py', 'PYMODULE'),
  ('ast', '/usr/local/lib/python3.12/ast.py', 'PYMODULE'),
  ('base64', '/usr/local/lib/python3.12/base64.py', 'PYMODULE'),
  ('bisect', '/usr/local/lib/python3.12/bisect.py', 'PYMODULE'),
  ('bz2', '/usr/local/lib/python3.12/bz2.py', 'PYMODULE'),
  ('calendar', '/usr/local/lib/python3.12/calendar.py', 'PYMODULE'),
  ('contextlib', '/usr/local/lib/python3.12/contextlib.py', 'PYMODULE'),
  ('contextvars', '/usr/local/lib/python3.12/contextvars.py', 'PYMODULE'),
  ('copy', '/usr/local/lib/python3.12/copy.py', 'PYMODULE'),
  ('csv', '/usr/local/lib/python3.12/csv.py', 'PYMODULE'),
  ('dataclasses', '/usr/local/lib/python3.12/dataclasses.py', 'PYMODULE'),
  ('datetime', '/usr/local/lib/python3.12/datetime.py', 'PYMODULE'),
  ('decimal', '/usr/local/lib/python3.12/decimal.py', 'PYMODULE'),
  ('dis', '/usr/local/lib/python3.12/dis.py', 'PYMODULE'),
  ('email', '/usr/local/lib/python3.12/email/__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   '/usr/local/lib/python3.12/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/usr/local/lib/python3.12/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/usr/local/lib/python3.12/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/usr/local/lib/python3.12/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/usr/local/lib/python3.12/email/base64mime.py',
   'PYMODULE'),
  ('email.charset', '/usr/local/lib/python3.12/email/charset.py', 'PYMODULE'),
  ('email.contentmanager',
   '/usr/local/lib/python3.12/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders', '/usr/local/lib/python3.12/email/encoders.py', 'PYMODULE'),
  ('email.errors', '/usr/local/lib/python3.12/email/errors.py', 'PYMODULE'),
  ('email.feedparser',
   '/usr/local/lib/python3.12/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/usr/local/lib/python3.12/email/generator.py',
   'PYMODULE'),
  ('email.header', '/usr/local/lib/python3.12/email/header.py', 'PYMODULE'),
  ('email.headerregistry',
   '/usr/local/lib/python3.12/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/usr/local/lib/python3.12/email/iterators.py',
   'PYMODULE'),
  ('email.message', '/usr/local/lib/python3.12/email/message.py', 'PYMODULE'),
  ('email.parser', '/usr/local/lib/python3.12/email/parser.py', 'PYMODULE'),
  ('email.policy', '/usr/local/lib/python3.12/email/policy.py', 'PYMODULE'),
  ('email.quoprimime',
   '/usr/local/lib/python3.12/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils', '/usr/local/lib/python3.12/email/utils.py', 'PYMODULE'),
  ('fnmatch', '/usr/local/lib/python3.12/fnmatch.py', 'PYMODULE'),
  ('fractions', '/usr/local/lib/python3.12/fractions.py', 'PYMODULE'),
  ('getopt', '/usr/local/lib/python3.12/getopt.py', 'PYMODULE'),
  ('gettext', '/usr/local/lib/python3.12/gettext.py', 'PYMODULE'),
  ('gzip', '/usr/local/lib/python3.12/gzip.py', 'PYMODULE'),
  ('hashlib', '/usr/local/lib/python3.12/hashlib.py', 'PYMODULE'),
  ('importlib', '/usr/local/lib/python3.12/importlib/__init__.py', 'PYMODULE'),
  ('importlib._abc', '/usr/local/lib/python3.12/importlib/_abc.py', 'PYMODULE'),
  ('importlib._bootstrap',
   '/usr/local/lib/python3.12/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/usr/local/lib/python3.12/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc', '/usr/local/lib/python3.12/importlib/abc.py', 'PYMODULE'),
  ('importlib.machinery',
   '/usr/local/lib/python3.12/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/usr/local/lib/python3.12/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/usr/local/lib/python3.12/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/usr/local/lib/python3.12/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/usr/local/lib/python3.12/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/usr/local/lib/python3.12/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/usr/local/lib/python3.12/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/usr/local/lib/python3.12/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/usr/local/lib/python3.12/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/usr/local/lib/python3.12/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/usr/local/lib/python3.12/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/usr/local/lib/python3.12/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/usr/local/lib/python3.12/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/usr/local/lib/python3.12/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/usr/local/lib/python3.12/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/usr/local/lib/python3.12/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util', '/usr/local/lib/python3.12/importlib/util.py', 'PYMODULE'),
  ('inspect', '/usr/local/lib/python3.12/inspect.py', 'PYMODULE'),
  ('ipaddress', '/usr/local/lib/python3.12/ipaddress.py', 'PYMODULE'),
  ('logging', '/usr/local/lib/python3.12/logging/__init__.py', 'PYMODULE'),
  ('lzma', '/usr/local/lib/python3.12/lzma.py', 'PYMODULE'),
  ('numbers', '/usr/local/lib/python3.12/numbers.py', 'PYMODULE'),
  ('opcode', '/usr/local/lib/python3.12/opcode.py', 'PYMODULE'),
  ('pathlib', '/usr/local/lib/python3.12/pathlib.py', 'PYMODULE'),
  ('pickle', '/usr/local/lib/python3.12/pickle.py', 'PYMODULE'),
  ('pkgutil', '/usr/local/lib/python3.12/pkgutil.py', 'PYMODULE'),
  ('platform', '/usr/local/lib/python3.12/platform.py', 'PYMODULE'),
  ('pprint', '/usr/local/lib/python3.12/pprint.py', 'PYMODULE'),
  ('py_compile', '/usr/local/lib/python3.12/py_compile.py', 'PYMODULE'),
  ('quopri', '/usr/local/lib/python3.12/quopri.py', 'PYMODULE'),
  ('random', '/usr/local/lib/python3.12/random.py', 'PYMODULE'),
  ('selectors', '/usr/local/lib/python3.12/selectors.py', 'PYMODULE'),
  ('shutil', '/usr/local/lib/python3.12/shutil.py', 'PYMODULE'),
  ('signal', '/usr/local/lib/python3.12/signal.py', 'PYMODULE'),
  ('socket', '/usr/local/lib/python3.12/socket.py', 'PYMODULE'),
  ('statistics', '/usr/local/lib/python3.12/statistics.py', 'PYMODULE'),
  ('string', '/usr/local/lib/python3.12/string.py', 'PYMODULE'),
  ('stringprep', '/usr/local/lib/python3.12/stringprep.py', 'PYMODULE'),
  ('subprocess', '/usr/local/lib/python3.12/subprocess.py', 'PYMODULE'),
  ('tarfile', '/usr/local/lib/python3.12/tarfile.py', 'PYMODULE'),
  ('tempfile', '/usr/local/lib/python3.12/tempfile.py', 'PYMODULE'),
  ('textwrap', '/usr/local/lib/python3.12/textwrap.py', 'PYMODULE'),
  ('threading', '/usr/local/lib/python3.12/threading.py', 'PYMODULE'),
  ('token', '/usr/local/lib/python3.12/token.py', 'PYMODULE'),
  ('tokenize', '/usr/local/lib/python3.12/tokenize.py', 'PYMODULE'),
  ('tracemalloc', '/usr/local/lib/python3.12/tracemalloc.py', 'PYMODULE'),
  ('typing', '/usr/local/lib/python3.12/typing.py', 'PYMODULE'),
  ('urllib', '/usr/local/lib/python3.12/urllib/__init__.py', 'PYMODULE'),
  ('urllib.parse', '/usr/local/lib/python3.12/urllib/parse.py', 'PYMODULE'),
  ('zipfile', '/usr/local/lib/python3.12/zipfile/__init__.py', 'PYMODULE'),
  ('zipfile._path',
   '/usr/local/lib/python3.12/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/usr/local/lib/python3.12/zipfile/_path/glob.py',
   'PYMODULE'),
  ('zipimport', '/usr/local/lib/python3.12/zipimport.py', 'PYMODULE')])
