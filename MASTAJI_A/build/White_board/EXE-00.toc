('/media/mastaji/文件/小马助教开发/MASTAJI_A/dist/White_board',
 False,
 False,
 False,
 None,
 None,
 False,
 False,
 None,
 True,
 False,
 None,
 None,
 None,
 '/media/mastaji/文件/小马助教开发/MASTAJI_A/build/White_board/White_board.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/build/White_board/PYZ-00.pyz',
   'PYZ'),
  ('lib-dynload/_struct.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_struct.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/zlib.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('struct',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/build/White_board/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/build/White_board/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/build/White_board/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/build/White_board/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('White_board',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/White_board.py',
   'PYSOURCE'),
  ('libpython3.12.so.1.0', '/usr/local/lib/libpython3.12.so.1.0', 'BINARY'),
  ('PyQt5/Qt5/plugins/xcbglintegrations/libqxcb-egl-integration.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/xcbglintegrations/libqxcb-egl-integration.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforminputcontexts/libibusplatforminputcontextplugin.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforminputcontexts/libibusplatforminputcontextplugin.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-shell-integration/libwl-shell.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-shell-integration/libwl-shell.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqico.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqico.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-graphics-integration-client/libqt-plugin-wayland-egl.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-graphics-integration-client/libqt-plugin-wayland-egl.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-decoration-client/libbradient.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-decoration-client/libbradient.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqwayland-egl.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqwayland-egl.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqgif.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqgif.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwebp.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqwebp.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-graphics-integration-client/libdmabuf-server.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-graphics-integration-client/libdmabuf-server.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqwayland-generic.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqwayland-generic.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqvnc.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqvnc.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqminimal.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqminimal.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqicns.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqicns.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqminimalegl.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqminimalegl.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/iconengines/libqsvgicon.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/iconengines/libqsvgicon.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtga.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqtga.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqwbmp.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqwbmp.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqjpeg.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqjpeg.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/generic/libqevdevtabletplugin.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/generic/libqevdevtabletplugin.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/xcbglintegrations/libqxcb-glx-integration.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/xcbglintegrations/libqxcb-glx-integration.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqxcb.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqxcb.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-graphics-integration-client/libxcomposite-glx.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-graphics-integration-client/libxcomposite-glx.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/generic/libqevdevkeyboardplugin.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/generic/libqevdevkeyboardplugin.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-shell-integration/libxdg-shell.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-shell-integration/libxdg-shell.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqlinuxfb.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqlinuxfb.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforminputcontexts/libcomposeplatforminputcontextplugin.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforminputcontexts/libcomposeplatforminputcontextplugin.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqoffscreen.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqoffscreen.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-shell-integration/libfullscreen-shell-v1.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-shell-integration/libfullscreen-shell-v1.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqeglfs.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqeglfs.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-graphics-integration-client/libxcomposite-egl.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-graphics-integration-client/libxcomposite-egl.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqtiff.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqtiff.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platformthemes/libqxdgdesktopportal.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/generic/libqevdevtouchplugin.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/generic/libqevdevtouchplugin.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqwebgl.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqwebgl.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-shell-integration/libxdg-shell-v6.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-shell-integration/libxdg-shell-v6.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/imageformats/libqsvg.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/imageformats/libqsvg.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-graphics-integration-client/libdrm-egl-server.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-graphics-integration-client/libdrm-egl-server.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqwayland-xcomposite-egl.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqwayland-xcomposite-egl.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-shell-integration/libxdg-shell-v5.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-shell-integration/libxdg-shell-v5.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/platforms/libqwayland-xcomposite-glx.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/platforms/libqwayland-xcomposite-glx.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/generic/libqevdevmouseplugin.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/generic/libqevdevmouseplugin.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-graphics-integration-client/libshm-emulation-server.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-graphics-integration-client/libshm-emulation-server.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/generic/libqtuiotouchplugin.so',
   'BINARY'),
  ('PyQt5/Qt5/plugins/wayland-shell-integration/libivi-shell.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/plugins/wayland-shell-integration/libivi-shell.so',
   'BINARY'),
  ('lib-dynload/_statistics.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_statistics.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_contextvars.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_decimal.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_pickle.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_hashlib.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_sha3.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_blake2.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_md5.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_sha1.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_sha2.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_random.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_bisect.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/math.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/grp.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/resource.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_lzma.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_bz2.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/unicodedata.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/unicodedata.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/array.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/select.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_socket.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_csv.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/binascii.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_opcode.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_heapq.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PyQt5/QtCore.abi3.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/QtCore.abi3.so',
   'EXTENSION'),
  ('PyQt5/sip.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/sip.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PyQt5/QtGui.abi3.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/QtGui.abi3.so',
   'EXTENSION'),
  ('PyQt5/QtWidgets.abi3.so',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/QtWidgets.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_datetime.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/_posixsubprocess.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/lib-dynload/fcntl.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('libbrotlidec.so.1', '/lib/x86_64-linux-gnu/libbrotlidec.so.1', 'BINARY'),
  ('libxkbcommon-x11.so.0',
   '/lib/x86_64-linux-gnu/libxkbcommon-x11.so.0',
   'BINARY'),
  ('libdbus-1.so.3', '/lib/x86_64-linux-gnu/libdbus-1.so.3', 'BINARY'),
  ('PyQt5/Qt5/lib/libQt5DBus.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5DBus.so.5',
   'BINARY'),
  ('libX11-xcb.so.1', '/lib/x86_64-linux-gnu/libX11-xcb.so.1', 'BINARY'),
  ('libxcb-shm.so.0', '/lib/x86_64-linux-gnu/libxcb-shm.so.0', 'BINARY'),
  ('libpng16.so.16', '/lib/x86_64-linux-gnu/libpng16.so.16', 'BINARY'),
  ('libexpat.so.1', '/lib/x86_64-linux-gnu/libexpat.so.1', 'BINARY'),
  ('libxcb-sync.so.1', '/lib/x86_64-linux-gnu/libxcb-sync.so.1', 'BINARY'),
  ('libxcb-render.so.0', '/lib/x86_64-linux-gnu/libxcb-render.so.0', 'BINARY'),
  ('PyQt5/Qt5/lib/libicui18n.so.56',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libicui18n.so.56',
   'BINARY'),
  ('libgthread-2.0.so.0',
   '/lib/x86_64-linux-gnu/libgthread-2.0.so.0',
   'BINARY'),
  ('libXau.so.6', '/lib/x86_64-linux-gnu/libXau.so.6', 'BINARY'),
  ('libgpg-error.so.0', '/lib/x86_64-linux-gnu/libgpg-error.so.0', 'BINARY'),
  ('PyQt5/Qt5/lib/libQt5XcbQpa.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5XcbQpa.so.5',
   'BINARY'),
  ('libxcb-randr.so.0', '/lib/x86_64-linux-gnu/libxcb-randr.so.0', 'BINARY'),
  ('PyQt5/Qt5/lib/libicudata.so.56',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libicudata.so.56',
   'BINARY'),
  ('libxcb-xinerama.so.0',
   '/lib/x86_64-linux-gnu/libxcb-xinerama.so.0',
   'BINARY'),
  ('libfreetype.so.6', '/lib/x86_64-linux-gnu/libfreetype.so.6', 'BINARY'),
  ('libbsd.so.0', '/lib/x86_64-linux-gnu/libbsd.so.0', 'BINARY'),
  ('libfontconfig.so.1', '/lib/x86_64-linux-gnu/libfontconfig.so.1', 'BINARY'),
  ('libstdc++.so.6', '/lib/x86_64-linux-gnu/libstdc++.so.6', 'BINARY'),
  ('libxcb-render-util.so.0',
   '/lib/x86_64-linux-gnu/libxcb-render-util.so.0',
   'BINARY'),
  ('libglib-2.0.so.0', '/lib/x86_64-linux-gnu/libglib-2.0.so.0', 'BINARY'),
  ('libgcrypt.so.20', '/lib/x86_64-linux-gnu/libgcrypt.so.20', 'BINARY'),
  ('libbrotlicommon.so.1',
   '/lib/x86_64-linux-gnu/libbrotlicommon.so.1',
   'BINARY'),
  ('libxcb-icccm.so.4', '/lib/x86_64-linux-gnu/libxcb-icccm.so.4', 'BINARY'),
  ('libxcb-image.so.0', '/lib/x86_64-linux-gnu/libxcb-image.so.0', 'BINARY'),
  ('libz.so.1', '/lib/x86_64-linux-gnu/libz.so.1', 'BINARY'),
  ('libX11.so.6', '/lib/x86_64-linux-gnu/libX11.so.6', 'BINARY'),
  ('libxcb-util.so.0', '/lib/x86_64-linux-gnu/libxcb-util.so.0', 'BINARY'),
  ('libXext.so.6', '/lib/x86_64-linux-gnu/libXext.so.6', 'BINARY'),
  ('liblzma.so.5', '/lib/x86_64-linux-gnu/liblzma.so.5', 'BINARY'),
  ('libgcc_s.so.1', '/lib/x86_64-linux-gnu/libgcc_s.so.1', 'BINARY'),
  ('libXdmcp.so.6', '/lib/x86_64-linux-gnu/libXdmcp.so.6', 'BINARY'),
  ('libxcb-keysyms.so.1',
   '/lib/x86_64-linux-gnu/libxcb-keysyms.so.1',
   'BINARY'),
  ('libxkbcommon.so.0', '/lib/x86_64-linux-gnu/libxkbcommon.so.0', 'BINARY'),
  ('libpcre.so.3', '/lib/x86_64-linux-gnu/libpcre.so.3', 'BINARY'),
  ('libxcb-xkb.so.1', '/lib/x86_64-linux-gnu/libxcb-xkb.so.1', 'BINARY'),
  ('libsystemd.so.0', '/lib/x86_64-linux-gnu/libsystemd.so.0', 'BINARY'),
  ('libxcb-shape.so.0', '/lib/x86_64-linux-gnu/libxcb-shape.so.0', 'BINARY'),
  ('PyQt5/Qt5/lib/libQt5Core.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5Core.so.5',
   'BINARY'),
  ('libxcb-xfixes.so.0', '/lib/x86_64-linux-gnu/libxcb-xfixes.so.0', 'BINARY'),
  ('libuuid.so.1', '/lib/x86_64-linux-gnu/libuuid.so.1', 'BINARY'),
  ('PyQt5/Qt5/lib/libQt5Gui.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5Gui.so.5',
   'BINARY'),
  ('liblz4.so.1', '/lib/x86_64-linux-gnu/liblz4.so.1', 'BINARY'),
  ('PyQt5/Qt5/lib/libicuuc.so.56',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libicuuc.so.56',
   'BINARY'),
  ('PyQt5/Qt5/lib/libQt5WaylandClient.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5WaylandClient.so.5',
   'BINARY'),
  ('libffi.so.6', '/lib/x86_64-linux-gnu/libffi.so.6', 'BINARY'),
  ('libcom_err.so.2', '/lib/x86_64-linux-gnu/libcom_err.so.2', 'BINARY'),
  ('libkeyutils.so.1', '/lib/x86_64-linux-gnu/libkeyutils.so.1', 'BINARY'),
  ('libk5crypto.so.3', '/lib/x86_64-linux-gnu/libk5crypto.so.3', 'BINARY'),
  ('libkrb5.so.3', '/lib/x86_64-linux-gnu/libkrb5.so.3', 'BINARY'),
  ('libgssapi_krb5.so.2',
   '/lib/x86_64-linux-gnu/libgssapi_krb5.so.2',
   'BINARY'),
  ('libkrb5support.so.0',
   '/lib/x86_64-linux-gnu/libkrb5support.so.0',
   'BINARY'),
  ('PyQt5/Qt5/lib/libQt5Network.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5Network.so.5',
   'BINARY'),
  ('PyQt5/Qt5/lib/libQt5Widgets.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5Widgets.so.5',
   'BINARY'),
  ('PyQt5/Qt5/lib/libQt5Svg.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5Svg.so.5',
   'BINARY'),
  ('libxcb-glx.so.0', '/lib/x86_64-linux-gnu/libxcb-glx.so.0', 'BINARY'),
  ('libXcomposite.so.1', '/lib/x86_64-linux-gnu/libXcomposite.so.1', 'BINARY'),
  ('PyQt5/Qt5/lib/libQt5EglFSDeviceIntegration.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5EglFSDeviceIntegration.so.5',
   'BINARY'),
  ('PyQt5/Qt5/lib/libQt5Qml.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5Qml.so.5',
   'BINARY'),
  ('PyQt5/Qt5/lib/libQt5QmlModels.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5QmlModels.so.5',
   'BINARY'),
  ('PyQt5/Qt5/lib/libQt5WebSockets.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5WebSockets.so.5',
   'BINARY'),
  ('PyQt5/Qt5/lib/libQt5Quick.so.5',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/lib/libQt5Quick.so.5',
   'BINARY'),
  ('libcrypto.so.1.1', '/lib/x86_64-linux-gnu/libcrypto.so.1.1', 'BINARY'),
  ('libbz2.so.1.0', '/lib/x86_64-linux-gnu/libbz2.so.1.0', 'BINARY'),
  ('Ppt_Control.py',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/Ppt_Control.py',
   'DATA'),
  ('SysCss.py', '/media/mastaji/文件/小马助教开发/MASTAJI_A/SysCss.py', 'DATA'),
  ('images/0.png', '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/0.png', 'DATA'),
  ('images/00.png', '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/00.png', 'DATA'),
  ('images/Clear0.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/Clear0.png',
   'DATA'),
  ('images/Clear1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/Clear1.png',
   'DATA'),
  ('images/Clear2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/Clear2.png',
   'DATA'),
  ('images/DClose0.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/DClose0.png',
   'DATA'),
  ('images/DClose1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/DClose1.png',
   'DATA'),
  ('images/DClose2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/DClose2.png',
   'DATA'),
  ('images/Magnify.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/Magnify.png',
   'DATA'),
  ('images/Magnify1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/Magnify1.png',
   'DATA'),
  ('images/Magnify2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/Magnify2.png',
   'DATA'),
  ('images/capture_screen.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/capture_screen.png',
   'DATA'),
  ('images/capture_screen1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/capture_screen1.png',
   'DATA'),
  ('images/capture_screen2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/capture_screen2.png',
   'DATA'),
  ('images/chart.js',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/chart.js',
   'DATA'),
  ('images/erasure0.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/erasure0.png',
   'DATA'),
  ('images/erasure1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/erasure1.png',
   'DATA'),
  ('images/erasure2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/erasure2.png',
   'DATA'),
  ('images/logo.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/logo.png',
   'DATA'),
  ('images/plotting0.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/plotting0.png',
   'DATA'),
  ('images/plotting1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/plotting1.png',
   'DATA'),
  ('images/plotting2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/plotting2.png',
   'DATA'),
  ('images/ppt.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt.png',
   'DATA'),
  ('images/ppt1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt1.png',
   'DATA'),
  ('images/ppt2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt2.png',
   'DATA'),
  ('images/ppt_End.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt_End.png',
   'DATA'),
  ('images/ppt_End1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt_End1.png',
   'DATA'),
  ('images/ppt_End2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt_End2.png',
   'DATA'),
  ('images/ppt_left.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt_left.png',
   'DATA'),
  ('images/ppt_left1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt_left1.png',
   'DATA'),
  ('images/ppt_left2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt_left2.png',
   'DATA'),
  ('images/ppt_right.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt_right.png',
   'DATA'),
  ('images/ppt_right1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt_right1.png',
   'DATA'),
  ('images/ppt_right2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/ppt_right2.png',
   'DATA'),
  ('images/record.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/record.png',
   'DATA'),
  ('images/record1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/record1.png',
   'DATA'),
  ('images/record2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/record2.png',
   'DATA'),
  ('images/revoke0.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/revoke0.png',
   'DATA'),
  ('images/revoke1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/revoke1.png',
   'DATA'),
  ('images/revoke2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/revoke2.png',
   'DATA'),
  ('images/setGN0.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/setGN0.png',
   'DATA'),
  ('images/setGN1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/setGN1.png',
   'DATA'),
  ('images/setGN2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/setGN2.png',
   'DATA'),
  ('images/setGN480.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/setGN480.png',
   'DATA'),
  ('images/setGN481.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/setGN481.png',
   'DATA'),
  ('images/setGN482.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/setGN482.png',
   'DATA'),
  ('images/spotlight0.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/spotlight0.png',
   'DATA'),
  ('images/spotlight1.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/spotlight1.png',
   'DATA'),
  ('images/spotlight2.png',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/images/spotlight2.png',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_tr.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_de.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pl.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lt.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_lt.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nn.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_gl.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ko.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ru.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_de.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_cs.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sl.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_bg.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ca.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_es.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_lv.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ja.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ja.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_pl.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_es.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fi.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_fr.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nn.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_nn.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_uk.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fr.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ja.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ja.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_uk.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ar.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_da.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ar.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_nl.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ar.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ar.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_bg.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_en.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_uk.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_uk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_en.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_it.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_cs.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_nl.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_BR.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_CN.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_he.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hu.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sl.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_sl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_zh_TW.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hr.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_bg.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_bg.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_it.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ko.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fa.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ko.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ko.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pl.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_pl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sv.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_sk.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fr.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gd.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_gl.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_gl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_hr.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_fa.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_fa.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_hu.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_he.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_he.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_tr.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_es.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_es.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_tr.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_tr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_it.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_it.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_lv.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_lv.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_ca.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_de.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_de.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hu.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_hu.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ru.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_cs.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_cs.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_hr.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_hr.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_ru.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_ru.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_da.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_nl.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_nl.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_ca.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_ca.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_sk.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_fi.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_fi.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_pt_PT.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_pt_PT.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_gd.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_gd.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qtbase_en.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qtbase_en.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_sk.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_sk.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_da.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_da.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_pt_BR.qm',
   'DATA'),
  ('PyQt5/Qt5/translations/qt_help_nn.qm',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyQt5/Qt5/translations/qt_help_nn.qm',
   'DATA'),
  ('base_library.zip',
   '/media/mastaji/文件/小马助教开发/MASTAJI_A/build/White_board/base_library.zip',
   'DATA'),
  ('libQt5DBus.so.5', 'PyQt5/Qt5/lib/libQt5DBus.so.5', 'SYMLINK'),
  ('libicui18n.so.56', 'PyQt5/Qt5/lib/libicui18n.so.56', 'SYMLINK'),
  ('libQt5XcbQpa.so.5', 'PyQt5/Qt5/lib/libQt5XcbQpa.so.5', 'SYMLINK'),
  ('libicudata.so.56', 'PyQt5/Qt5/lib/libicudata.so.56', 'SYMLINK'),
  ('libQt5Core.so.5', 'PyQt5/Qt5/lib/libQt5Core.so.5', 'SYMLINK'),
  ('libQt5Gui.so.5', 'PyQt5/Qt5/lib/libQt5Gui.so.5', 'SYMLINK'),
  ('libicuuc.so.56', 'PyQt5/Qt5/lib/libicuuc.so.56', 'SYMLINK'),
  ('libQt5WaylandClient.so.5',
   'PyQt5/Qt5/lib/libQt5WaylandClient.so.5',
   'SYMLINK'),
  ('libQt5Network.so.5', 'PyQt5/Qt5/lib/libQt5Network.so.5', 'SYMLINK'),
  ('libQt5Widgets.so.5', 'PyQt5/Qt5/lib/libQt5Widgets.so.5', 'SYMLINK'),
  ('libQt5Svg.so.5', 'PyQt5/Qt5/lib/libQt5Svg.so.5', 'SYMLINK'),
  ('libQt5EglFSDeviceIntegration.so.5',
   'PyQt5/Qt5/lib/libQt5EglFSDeviceIntegration.so.5',
   'SYMLINK'),
  ('libQt5Qml.so.5', 'PyQt5/Qt5/lib/libQt5Qml.so.5', 'SYMLINK'),
  ('libQt5QmlModels.so.5', 'PyQt5/Qt5/lib/libQt5QmlModels.so.5', 'SYMLINK'),
  ('libQt5WebSockets.so.5', 'PyQt5/Qt5/lib/libQt5WebSockets.so.5', 'SYMLINK'),
  ('libQt5Quick.so.5', 'PyQt5/Qt5/lib/libQt5Quick.so.5', 'SYMLINK')],
 [],
 False,
 False,
 1746856029,
 [('run',
   '/home/<USER>/.local/lib/python3.12/site-packages/PyInstaller/bootloader/Linux-64bit-intel/run',
   'EXECUTABLE')],
 '/usr/local/lib/libpython3.12.so.1.0')
