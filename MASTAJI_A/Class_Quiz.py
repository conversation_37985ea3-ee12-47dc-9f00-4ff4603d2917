import sys
import json
import yagmail
from PyQt5.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QTextEdit, QPushButton, QMessageBox, QFileDialog
)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont


class EmailSenderApp(QWidget):
    def __init__(self):
        super().__init__()
        self.initUI()

    def initUI(self):
        # 设置窗口标题和大小
        self.setWindowTitle("随堂测验")
        self.setGeometry(600, 300, 600, 500)
        self.setWindowFlags(Qt.Tool | self.windowFlags())


        # 设置字体
        font = QFont("Arial", 12)

        # 主布局
        main_layout = QVBoxLayout()

        # 发件人信息
        sender_layout = QHBoxLayout()
        sender_label = QLabel("发件人邮箱:")
        sender_label.setFont(font)
        self.sender_email_input = QLineEdit()
        self.sender_email_input.setFont(font)
        sender_layout.addWidget(sender_label)
        sender_layout.addWidget(self.sender_email_input)
        main_layout.addLayout(sender_layout)

        # 密码
        password_layout = QHBoxLayout()
        password_label = QLabel("邮箱密码:")
        password_label.setFont(font)
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setFont(font)
        password_layout.addWidget(password_label)
        password_layout.addWidget(self.password_input)
        main_layout.addLayout(password_layout)

        # 主题
        subject_layout = QHBoxLayout()
        subject_label = QLabel("邮件主题:")
        subject_label.setFont(font)
        self.subject_input = QLineEdit()
        self.subject_input.setFont(font)
        subject_layout.addWidget(subject_label)
        subject_layout.addWidget(self.subject_input)
        main_layout.addLayout(subject_layout)

        # 内容
        content_label = QLabel("邮件内容:")
        content_label.setFont(font)
        self.content_input = QTextEdit()
        self.content_input.setFont(font)
        main_layout.addWidget(content_label)
        main_layout.addWidget(self.content_input)

        # 附件
        attachment_layout = QHBoxLayout()
        attachment_label = QLabel("附件:")
        attachment_label.setFont(font)
        self.attachment_input = QLineEdit()
        self.attachment_input.setFont(font)
        self.attachment_input.setReadOnly(True)
        self.browse_button = QPushButton("选择文件")
        self.browse_button.setFont(font)
        self.browse_button.clicked.connect(self.browse_file)
        attachment_layout.addWidget(attachment_label)
        attachment_layout.addWidget(self.attachment_input)
        attachment_layout.addWidget(self.browse_button)
        main_layout.addLayout(attachment_layout)

        # 按钮布局
        button_layout = QHBoxLayout()
        self.load_button = QPushButton("加载学生表")
        self.load_button.setFont(font)
        self.load_button.clicked.connect(self.load_student_list)
        self.send_button = QPushButton("发送邮件")
        self.send_button.setFont(font)
        self.send_button.clicked.connect(self.send_emails)
        button_layout.addWidget(self.load_button)
        button_layout.addWidget(self.send_button)
        main_layout.addLayout(button_layout)

        # 设置主布局
        self.setLayout(main_layout)

    def browse_file(self):
        # 打开文件对话框选择附件
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择附件", "", "All Files (*);;Word Files (*.docx);;PPT Files (*.ppt *.pptx);;PDF Files (*.pdf)")
        if file_path:
            self.attachment_input.setText(file_path)

    def load_student_list(self):
        # 打开文件对话框选择学生表
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择学生表", "", "JSON Files (*.json)")
        if file_path:
            try:
                with open(file_path, "r", encoding="utf-8") as file:
                    self.student_list = json.load(file)
                QMessageBox.information(self, "成功", "学生表加载成功！")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载学生表失败: {str(e)}")

    def send_emails(self):
        # 获取输入信息
        sender_email = self.sender_email_input.text()
        password = self.password_input.text()
        subject = self.subject_input.text()
        content = self.content_input.toPlainText()
        attachment_path = self.attachment_input.text()

        # 验证输入
        if not sender_email or not password or not subject or not content:
            QMessageBox.warning(self, "警告", "请填写完整信息！")
            return

        if not hasattr(self, 'student_list'):
            QMessageBox.warning(self, "警告", "请先加载学生表！")
            return

        # 发送邮件
        try:
            yag = yagmail.SMTP(sender_email, password)
            for student in self.student_list:
                to_email = student["email"]
                if attachment_path:
                    yag.send(to=to_email, subject=subject,
                             contents=content, attachments=attachment_path)
                else:
                    yag.send(to=to_email, subject=subject, contents=content)
            QMessageBox.information(self, "成功", "邮件发送成功！")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"邮件发送失败: {str(e)}")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = EmailSenderApp()
    window.show()
    sys.exit(app.exec_())
