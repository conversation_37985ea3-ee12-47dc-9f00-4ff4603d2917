<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课堂报告可视化</title>
    <script src="images/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }

        .chart-container {
            width: 45%;
            display: inline-block;
            margin: 10px;
        }

        .questions {
            margin-top: 20px;
        }
    </style>
</head>

<body>
    <h1>课堂报告可视化</h1>
    <div class="chart-container">
        <canvas id="attendanceChart"></canvas>
    </div>
    <div class="chart-container">
        <canvas id="participationChart"></canvas>
    </div>
    <div class="chart-container">
        <canvas id="interactionsChart"></canvas>
    </div>
    <div class="questions">
        <h2>课堂提问</h2>
        <ul id="questionsList"></ul>
    </div>
    <div class="notes">
        <h2>备注</h2>
        <p id="notesText"></p>
    </div>

    <script>
        // JSON 数据  
        const reportData = {
            "class_name": "Python 编程课",
            "date": "2023-10-05",
            "attendance": {
                "Alice": "Present",
                "Bob": "Late",
                "Charlie": "Absent"
            },
            "interactions": 2,
            "participation": {
                "Alice": "High",
                "Bob": "Medium",
                "Charlie": "Low"
            },
            "questions": [
                "如何在 Python 中使用字典？",
                "列表和元组有什么区别？"
            ],
            "notes": "下次课需要讲解更多字典方法。"
        };

        // 渲染考勤状态图表  
        const attendanceCtx = document.getElementById('attendanceChart').getContext('2d');
        const attendanceStatus = {
            Present: 0,
            Late: 0,
            Absent: 0
        };
        for (const status of Object.values(reportData.attendance)) {
            attendanceStatus[status]++;
        }
        new Chart(attendanceCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(attendanceStatus),
                datasets: [{
                    label: '考勤状态',
                    data: Object.values(attendanceStatus),
                    backgroundColor: ['green', 'orange', 'red']
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 渲染学生参与度图表  
        const participationCtx = document.getElementById('participationChart').getContext('2d');
        const participationLevels = {
            High: 0,
            Medium: 0,
            Low: 0
        };
        for (const level of Object.values(reportData.participation)) {
            participationLevels[level]++;
        }
        new Chart(participationCtx, {
            type: 'bar',
            data: {
                labels: Object.keys(participationLevels),
                datasets: [{
                    label: '学生参与度',
                    data: Object.values(participationLevels),
                    backgroundColor: ['blue', 'purple', 'gray']
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 渲染互动次数图表  
        const interactionsCtx = document.getElementById('interactionsChart').getContext('2d');
        new Chart(interactionsCtx, {
            type: 'bar',
            data: {
                labels: ['互动次数'],
                datasets: [{
                    label: '互动次数',
                    data: [reportData.interactions],
                    backgroundColor: 'orange'
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // 渲染课堂提问  
        const questionsList = document.getElementById('questionsList');
        reportData.questions.forEach(question => {
            const li = document.createElement('li');
            li.textContent = question;
            questionsList.appendChild(li);
        });

        // 渲染备注  
        const notesText = document.getElementById('notesText');
        notesText.textContent = reportData.notes;  
    </script>
</body>

</html>