#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试登录系统修复效果的脚本
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMessageBox

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_login_manager():
    """测试登录管理器功能"""
    print("=== 测试登录管理器 ===")
    
    from login_manager import get_login_manager
    
    login_manager = get_login_manager()
    print(f"登录管理器初始化完成")
    print(f"当前登录状态: {login_manager.is_logged_in}")
    
    if login_manager.is_logged_in:
        teacher_info = login_manager.get_teacher_info()
        print(f"已登录教师: {teacher_info.get('teacher_name', '未知')}")
        print(f"教师ID: {teacher_info.get('user_id', '未知')}")
    else:
        print("当前未登录")
    
    return login_manager

def test_select_people_direct():
    """测试直接运行Select_People"""
    print("\n=== 测试直接运行Select_People ===")
    
    try:
        from Select_People import RandomStudentPicker
        
        app = QApplication(sys.argv)
        
        # 直接创建RandomStudentPicker，不传入teacher_info
        picker = RandomStudentPicker()
        
        print("Select_People创建成功")
        print(f"教师信息: {picker.teacher_info}")
        print(f"Session状态: {'有效' if picker.session else '无效'}")
        
        # 不显示界面，只测试初始化
        # picker.show()
        # app.exec_()
        
        return True
        
    except Exception as e:
        print(f"Select_People测试失败: {e}")
        return False

def test_mastaji_direct():
    """测试直接运行Mastaji"""
    print("\n=== 测试直接运行Mastaji ===")
    
    try:
        from Mastaji import MainWindow
        
        app = QApplication(sys.argv)
        
        # 直接创建MainWindow，不传入teacher_info
        main_window = MainWindow()
        
        print("Mastaji创建成功")
        print(f"教师信息: {main_window.teacher_info}")
        
        # 不显示界面，只测试初始化
        # main_window.show()
        # app.exec_()
        
        return True
        
    except Exception as e:
        print(f"Mastaji测试失败: {e}")
        return False

def test_login_persistence():
    """测试登录状态持久化"""
    print("\n=== 测试登录状态持久化 ===")
    
    from login_manager import get_login_manager
    
    login_manager = get_login_manager()
    
    # 检查会话文件是否存在
    session_file = 'session_data.pkl'
    if os.path.exists(session_file):
        print(f"会话文件存在: {session_file}")
        
        # 尝试恢复会话
        restored = login_manager.restore_session()
        print(f"会话恢复结果: {restored}")
        
        if restored:
            teacher_info = login_manager.get_teacher_info()
            print(f"恢复的教师信息: {teacher_info}")
        
    else:
        print(f"会话文件不存在: {session_file}")
    
    return True

def main():
    """主测试函数"""
    print("开始测试登录系统修复效果...")
    
    # 测试1: 登录管理器基本功能
    login_manager = test_login_manager()
    
    # 测试2: 登录状态持久化
    test_login_persistence()
    
    # 测试3: 直接运行Select_People
    select_people_ok = test_select_people_direct()
    
    # 测试4: 直接运行Mastaji
    mastaji_ok = test_mastaji_direct()
    
    # 总结测试结果
    print("\n=== 测试结果总结 ===")
    print(f"登录管理器: 正常")
    print(f"Select_People直接运行: {'正常' if select_people_ok else '异常'}")
    print(f"Mastaji直接运行: {'正常' if mastaji_ok else '异常'}")
    
    if login_manager.is_logged_in:
        print(f"当前登录状态: 已登录 ({login_manager.teacher_info.get('teacher_name', '未知')})")
    else:
        print("当前登录状态: 未登录")
    
    print("\n测试完成！")

if __name__ == '__main__':
    main()
